<!DOCTYPE html>
<html lang="th" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบเมนูแฮมเบอร์เกอร์ - ระบบหลังบ้าน</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom Admin CSS -->
    <link href="public/css/admin-custom.css" rel="stylesheet">
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg top-navbar">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand" href="#">
                <img src="public/images/โลโก้ผู้ใหญ่ประจักษ์นกสีดำ.png" alt="โลโก้" class="me-2" style="height: 60px; width: auto;">
                ระบบหลังบ้าน ผู้ใหญ่ประจักษ์เซอร์วิส
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav nav-pills me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="fas fa-home me-2"></i>หน้าหลัก
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-cogs me-2"></i>บริการ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-box me-2"></i>แพ็คเกจ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-images me-2"></i>ผลงาน
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-image me-2"></i>แบนเนอร์
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                            <span class="badge bg-danger ms-1">3</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-cog me-2"></i>ตั้งค่า
                        </a>
                    </li>
                </ul>

                <!-- Right Side -->
                <div class="d-flex align-items-center gap-3">
                    <!-- User Dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>ผู้ใหญ่ประจักษ์เซอร์วิส
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-plus me-2"></i>เพิ่มบริการ
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-plus me-2"></i>เพิ่มแพ็คเกจ
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-plus me-2"></i>เพิ่มผลงาน
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>ดูเว็บไซต์
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-wrapper">
        <div class="container-fluid">
            <div class="content-safe-area">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="mb-0">
                                    <i class="fas fa-hamburger me-2"></i>
                                    ทดสอบเมนูแฮมเบอร์เกอร์
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h5><i class="fas fa-info-circle me-2"></i>วิธีทดสอบ:</h5>
                                    <ol>
                                        <li>ลดขนาดหน้าจอให้เล็กลง (กว้างน้อยกว่า 992px)</li>
                                        <li>คลิกปุ่มแฮมเบอร์เกอร์ (☰) ที่มุมขวาบนของ navbar</li>
                                        <li>ตรวจสอบว่าเมนูที่ขยายออกมามีพื้นหลังที่ชัดเจน</li>
                                        <li>ตรวจสอบว่าข้อความในเมนูอ่านได้ชัดเจน ไม่โปร่งใส</li>
                                        <li>ทดสอบคลิกลิงก์ต่างๆ ในเมนู</li>
                                    </ol>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">✅ ปัญหาที่แก้ไขแล้ว</h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled">
                                                    <li><i class="fas fa-check text-success me-2"></i>เพิ่มพื้นหลังสีขาวให้เมนูแฮมเบอร์เกอร์</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>เพิ่ม backdrop-filter สำหรับความชัดเจน</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>ปรับปรุงสีข้อความให้อ่านง่าย</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>เพิ่มเงาและขอบให้เมนู</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>ปรับปรุงปุ่มแฮมเบอร์เกอร์</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>เพิ่ม hover effects</li>
                                                    <li><i class="fas fa-check text-success me-2"></i>ปรับปรุง dropdown menu</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">🎨 การปรับปรุงที่เพิ่ม</h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled">
                                                    <li><i class="fas fa-star text-warning me-2"></i>Animation เมื่อเปิด/ปิดเมนู</li>
                                                    <li><i class="fas fa-star text-warning me-2"></i>Auto-close เมื่อคลิกข้างนอก</li>
                                                    <li><i class="fas fa-star text-warning me-2"></i>Scroll effect สำหรับ navbar</li>
                                                    <li><i class="fas fa-star text-warning me-2"></i>Responsive design ที่ดีขึ้น</li>
                                                    <li><i class="fas fa-star text-warning me-2"></i>Badge animation สำหรับการแจ้งเตือน</li>
                                                    <li><i class="fas fa-star text-warning me-2"></i>Improved accessibility</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <h5>📱 ทดสอบบนอุปกรณ์ต่างๆ:</h5>
                                    <div class="row">
                                        <div class="col-sm-6 col-md-3 mb-2">
                                            <button class="btn btn-outline-primary w-100" onclick="testViewport(576)">
                                                📱 Mobile (576px)
                                            </button>
                                        </div>
                                        <div class="col-sm-6 col-md-3 mb-2">
                                            <button class="btn btn-outline-primary w-100" onclick="testViewport(768)">
                                                📱 Tablet (768px)
                                            </button>
                                        </div>
                                        <div class="col-sm-6 col-md-3 mb-2">
                                            <button class="btn btn-outline-primary w-100" onclick="testViewport(992)">
                                                💻 Desktop (992px)
                                            </button>
                                        </div>
                                        <div class="col-sm-6 col-md-3 mb-2">
                                            <button class="btn btn-outline-primary w-100" onclick="testViewport(1200)">
                                                🖥️ Large (1200px)
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom Admin JS -->
    <script src="public/js/admin-custom.js"></script>
    <script src="public/js/admin-navbar-fix.js"></script>

    <script>
        function testViewport(width) {
            // จำลองการเปลี่ยนขนาดหน้าจอ
            document.body.style.maxWidth = width + 'px';
            document.body.style.margin = '0 auto';
            document.body.style.border = '2px solid #007bff';
            
            setTimeout(() => {
                document.body.style.maxWidth = '';
                document.body.style.margin = '';
                document.body.style.border = '';
            }, 3000);
            
            alert(`กำลังจำลองหน้าจอขนาด ${width}px เป็นเวลา 3 วินาที`);
        }

        // เพิ่มการแสดงสถานะของเมนู
        document.addEventListener('DOMContentLoaded', function() {
            const navbarCollapse = document.getElementById('navbarNav');
            const statusDiv = document.createElement('div');
            statusDiv.className = 'alert alert-secondary mt-3';
            statusDiv.innerHTML = '<strong>สถานะเมนู:</strong> <span id="menuStatus">ปิด</span>';
            
            document.querySelector('.card-body').appendChild(statusDiv);
            
            navbarCollapse.addEventListener('shown.bs.collapse', function() {
                document.getElementById('menuStatus').textContent = 'เปิด';
                document.getElementById('menuStatus').className = 'text-success';
            });
            
            navbarCollapse.addEventListener('hidden.bs.collapse', function() {
                document.getElementById('menuStatus').textContent = 'ปิด';
                document.getElementById('menuStatus').className = 'text-danger';
            });
        });
    </script>
</body>
</html>
