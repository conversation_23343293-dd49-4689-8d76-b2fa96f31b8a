===============================================
    คู่มือการเปลี่ยนฟอนต์ในเว็บไซต์
    ผู้ใหญ่ประจักร์ เซอร์วิส ช็อป
===============================================

📌 ฟอนต์ปัจจุบันที่ใช้อยู่:
   - Inter (ภาษาอังกฤษ)
   - Kanit (ภาษาไทย)

📌 ต้องแก้ไขทั้งหมด 6 จุด ใน 4 ไฟล์

===============================================
ไฟล์ที่ 1: หน้าเว็บหลัก (Frontend)
===============================================

📁 ไฟล์: resources\views\layouts\app.blade.php

🔸 จุดที่ 1 - บรรทัดที่ 14:
   หา: <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
   
   เปลี่ยนเป็น: <link href="https://fonts.googleapis.com/css2?family=ฟอนต์ใหม่ที่ต้องการ" rel="stylesheet">

🔸 จุดที่ 2 - บรรทัดที่ 20:
   หา: font-family: 'Inter', 'Kanit', sans-serif;
   
   เปลี่ยนเป็น: font-family: 'ฟอนต์ใหม่', sans-serif;

===============================================
ไฟล์ที่ 2: หน้า Admin
===============================================

📁 ไฟล์: resources\views\layouts\admin.blade.php

🔸 จุดที่ 3 - บรรทัดที่ 13:
   หา: <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
   
   เปลี่ยนเป็น: <link href="https://fonts.googleapis.com/css2?family=ฟอนต์ใหม่ที่ต้องการ" rel="stylesheet">

🔸 จุดที่ 4 - บรรทัดที่ 68:
   หา: font-family: 'Inter', 'Kanit', sans-serif;
   
   เปลี่ยนเป็น: font-family: 'ฟอนต์ใหม่', sans-serif;

===============================================
ไฟล์ที่ 3: CSS หน้าเว็บ
===============================================

📁 ไฟล์: public\css\funeral-style.css

🔸 จุดที่ 5 - บรรทัดที่ 286:
   หา: font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
   
   เปลี่ยนเป็น: font-family: 'ฟอนต์ใหม่', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

===============================================
ไฟล์ที่ 4: CSS หน้า Admin
===============================================

📁 ไฟล์: public\css\admin-custom.css

🔸 จุดที่ 6 - บรรทัดที่ 53:
   หา: font-family: 'Inter', 'Kanit', sans-serif;
   
   เปลี่ยนเป็น: font-family: 'ฟอนต์ใหม่', sans-serif;

===============================================
วิธีหาฟอนต์ใหม่จาก Google Fonts
===============================================

1. เปิดเว็บ https://fonts.google.com
2. เลือกฟอนต์ที่ชอบ
3. กดปุ่ม "Select this style" ที่น้ำหนักที่ต้องการ
4. กดปุ่ม "View selected families"
5. คัดลอก link ที่ขึ้นมา

===============================================
ตัวอย่างฟอนต์ที่แนะนำ
===============================================

🔤 ฟอนต์ภาษาไทย:
   - Noto Sans Thai (ฟอนต์ไทยใหม่)
   - Sarabun (ฟอนต์ไทยสวยงาม)
   - Prompt (ฟอนต์ไทยอ่านง่าย)

🔤 ฟอนต์ภาษาอังกฤษ:
   - Roboto (ฟอนต์ Google ยอดนิยม)
   - Open Sans (ฟอนต์อ่านง่าย)
   - Lato (ฟอนต์สวยงาม)

===============================================
ตัวอย่างการเปลี่ยน
===============================================

🎯 ถ้าต้องการเปลี่ยนเป็น Roboto และ Noto Sans Thai:

เปลี่ยน Google Fonts Link จาก:
https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Kanit:wght@300;400;500;600;700&display=swap

เป็น:
https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&family=Noto+Sans+Thai:wght@300;400;500;600;700&display=swap

เปลี่ยน CSS จาก:
font-family: 'Inter', 'Kanit', sans-serif;

เป็น:
font-family: 'Roboto', 'Noto Sans Thai', sans-serif;

===============================================
ข้อควรระวัง
===============================================

⚠️ ต้องแก้ไขครบทั้ง 6 จุด ถ้าแก้ไม่ครบจะมีหน้าบางหน้าฟอนต์ไม่เปลี่ยน
⚠️ ชื่อฟอนต์ต้องเขียนให้ตรงกับใน Google Fonts
⚠️ ควรเก็บ sans-serif ไว้ท้ายสุดเสมอ
⚠️ หลังแก้เสร็จให้รีเฟรชเว็บไซต์เพื่อดูผลลัพธ์

===============================================
สรุปไฟล์ที่ต้องแก้
===============================================

1. resources\views\layouts\app.blade.php (บรรทัด 14, 20)
2. resources\views\layouts\admin.blade.php (บรรทัด 13, 68)
3. public\css\funeral-style.css (บรรทัด 286)
4. public\css\admin-custom.css (บรรทัด 53)

===============================================
จบคู่มือ
===============================================
