# 📔 DATA DIARY - ตาราง migrations

## 📊 ข้อมูลพื้นฐาน

**ชื่อตาราง:** `migrations`  
**ประเภท:** ตารางระบบ Laravel  
**จำนวนฟิลด์:** 3 ฟิลด์  
**Primary Key:** `id` (INT)  

---

## 📋 โครงสร้างฟิลด์

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | `id` | INT | PK | รหัสลำดับการรัน (Auto Increment) |
| 2 | `migration` | VARCHAR(255) | - | ชื่อไฟล์ migration |
| 3 | `batch` | INT | - | กลุ่มการรัน migration |

---

## 🎯 วัตถุประสงค์การใช้งาน

- **ติดตามประวัติ:** เก็บบันทึกการสร้าง/แก้ไขโครงสร้างฐานข้อมูล
- **Version Control:** ควบคุมเวอร์ชันของฐานข้อมูล
- **Rollback System:** สามารถย้อนกลับการเปลี่ยนแปลงได้
- **Batch Management:** จัดกลุ่มการรัน migration

---

## 📈 ข้อมูลปัจจุบัน

### จำนวนรวม: **27 migration files**

### แบ่งตาม Batch:
- **Batch 1:** 4 ไฟล์ (<PERSON><PERSON> default)
- **Batch 2:** 5 ไฟล์ (Core tables)
- **Batch 3:** 2 ไฟล์ (Image system)
- **Batch 4:** 12 ไฟล์ (UI enhancement)
- **Batch 5:** 4 ไฟล์ (Final updates)

---

## 🔍 ตัวอย่างข้อมูล

```
id | migration                                    | batch
---|----------------------------------------------|-------
1  | 2014_10_12_000000_create_users_table        | 1
2  | 2025_07_16_181748_create_services_table     | 2
3  | 2025_07_16_181846_create_packages_table     | 2
4  | 2025_07_19_114857_create_banners_table      | 4
5  | 2025_07_20_171023_create_service_images_table | 5
```

---

## 🔧 คำสั่งที่ใช้บ่อย

### ดูสถานะ Migration
```bash
php artisan migrate:status
```

### รัน Migration
```bash
php artisan migrate
```

### ย้อนกลับ Migration
```bash
php artisan migrate:rollback
```

### สร้าง Migration ใหม่
```bash
php artisan make:migration create_table_name
```

---

## 📊 SQL Queries สำหรับตรวจสอบ

### ดูข้อมูลทั้งหมด
```sql
SELECT * FROM migrations ORDER BY id ASC;
```

### ดู Migration ล่าสุด
```sql
SELECT * FROM migrations 
WHERE batch = (SELECT MAX(batch) FROM migrations);
```

### นับจำนวนตาม Batch
```sql
SELECT batch, COUNT(*) as count 
FROM migrations 
GROUP BY batch 
ORDER BY batch;
```

### ค้นหา Migration เฉพาะ
```sql
SELECT * FROM migrations 
WHERE migration LIKE '%services%';
```

---

## ⚠️ ข้อควรระวัง

### 🚨 ห้ามทำ
- ❌ ลบตารางนี้
- ❌ แก้ไขข้อมูลโดยตรง
- ❌ รัน migration ในระบบจริงโดยไม่สำรอง

### ✅ ควรทำ
- ✅ สำรองฐานข้อมูลก่อน migrate
- ✅ ทดสอบใน development ก่อน
- ✅ ใช้ `--pretend` เพื่อดู SQL

---

## 📅 ประวัติการเปลี่ยนแปลง

### **2014-2019:** Laravel Default Setup
- สร้างตารางพื้นฐาน (users, password_resets, etc.)

### **16 July 2025:** Core Application
- สร้างตารางหลัก (services, packages, activities)
- ระบบการตั้งค่าและการติดต่อ

### **17 July 2025:** Image System
- เพิ่มระบบรูปภาพสำหรับ activities
- ปรับปรุงโครงสร้างราคา

### **19 July 2025:** UI Enhancement
- ระบบพื้นหลังและแบนเนอร์
- ปรับปรุงระบบลิงก์

### **20-21 July 2025:** Final Polish
- เพิ่มระบบรูปภาพสำหรับ services
- ปรับปรุงโครงสร้างให้สมบูรณ์

---

## 🔍 การแก้ไขปัญหา

### Migration ไม่รัน
```bash
php artisan config:clear
php artisan cache:clear
php artisan migrate:status
```

### Migration ขัดข้อง
```bash
php artisan migrate:rollback
php artisan migrate
```

### ตาราง migrations หาย
```sql
CREATE TABLE migrations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    migration VARCHAR(255) NOT NULL,
    batch INT NOT NULL
);
```

---

## 📝 หมายเหตุสำคัญ

1. **ตารางระบบ:** เป็นส่วนหนึ่งของ Laravel Framework
2. **ไม่ใช่ข้อมูลธุรกิจ:** ไม่เกี่ยวข้องกับเนื้อหาเว็บไซต์
3. **สำคัญมาก:** หากสูญหายจะทำให้ระบบ migration ใช้งานไม่ได้
4. **อัตโนมัติ:** Laravel จัดการข้อมูลในตารางนี้เอง

---

## 🎯 สรุป

ตาราง `migrations` เป็น **หัวใจของระบบ Version Control** สำหรับฐานข้อมูล ช่วยให้นักพัฒนาสามารถ:

- ติดตามการเปลี่ยนแปลงโครงสร้างฐานข้อมูล
- ย้อนกลับการเปลี่ยนแปลงเมื่อมีปัญหา
- ทำงานร่วมกันในทีมได้อย่างมีประสิทธิภาพ
- รักษาความสอดคล้องของฐานข้อมูลในสภาพแวดล้อมต่างๆ

**🔑 Key Point:** ตารางนี้เป็นตารางระบบ - ห้ามแก้ไขโดยตรง!

---

**📅 บันทึกล่าสุด:** 31 กรกฎาคม 2025  
**👨‍💻 ผู้บันทึก:** Augment Agent  
**🏷️ หมวดหมู่:** System Table / Laravel Framework
