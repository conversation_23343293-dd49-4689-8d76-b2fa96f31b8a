-- ========================================
-- Database Backup SQL - ระบบผู้ใหญ่ประจักร์เซอร์วิสช็อป
-- สร้างเมื่อ: 2025-07-31
-- ========================================

-- สร้างฐานข้อมูล
CREATE DATABASE IF NOT EXISTS `prajak_service_shop` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `prajak_service_shop`;

-- ========================================
-- ตาราง 1: USERS (ผู้ดูแลระบบ)
-- ========================================
CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลตัวอย่าง USERS
INSERT INTO `users` (`id`, `name`, `email`, `password`, `created_at`, `updated_at`) VALUES
(1, 'Admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW());

-- ========================================
-- ตาราง 2: SERVICES (บริการ)
-- ========================================
CREATE TABLE `services` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text,
  `price` decimal(10,2) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลตัวอย่าง SERVICES
INSERT INTO `services` (`id`, `title`, `description`, `price`, `status`, `created_at`, `updated_at`) VALUES
(1, 'ซ่อมคอมพิวเตอร์', 'บริการซ่อมคอมพิวเตอร์ทุกประเภท', 500.00, 'active', NOW(), NOW()),
(2, 'ติดตั้งซอฟต์แวร์', 'บริการติดตั้งซอฟต์แวร์และโปรแกรม', 300.00, 'active', NOW(), NOW()),
(3, 'ทำความสะอาดเครื่อง', 'บริการทำความสะอาดเครื่องคอมพิวเตอร์', 200.00, 'active', NOW(), NOW());

-- ========================================
-- ตาราง 3: SERVICE_IMAGES (รูปภาพบริการ)
-- ========================================
CREATE TABLE `service_images` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `service_id` bigint(20) UNSIGNED NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `service_images_service_id_foreign` (`service_id`),
  CONSTRAINT `service_images_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลตัวอย่าง SERVICE_IMAGES
INSERT INTO `service_images` (`id`, `service_id`, `image_path`, `alt_text`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 1, 'images/services/computer-repair.jpg', 'ซ่อมคอมพิวเตอร์', 1, NOW(), NOW()),
(2, 2, 'images/services/software-install.jpg', 'ติดตั้งซอฟต์แวร์', 1, NOW(), NOW()),
(3, 3, 'images/services/cleaning.jpg', 'ทำความสะอาด', 1, NOW(), NOW());

-- ========================================
-- ตาราง 4: PACKAGES (แพ็คเกจ)
-- ========================================
CREATE TABLE `packages` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `duration` varchar(100) DEFAULT NULL,
  `features` json DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลตัวอย่าง PACKAGES
INSERT INTO `packages` (`id`, `name`, `description`, `price`, `duration`, `status`, `created_at`, `updated_at`) VALUES
(1, 'แพ็คเกจพื้นฐาน', 'บริการซ่อมพื้นฐาน', 1000.00, '1 วัน', 'active', NOW(), NOW()),
(2, 'แพ็คเกจมาตรฐาน', 'บริการซ่อมครบวงจร', 2000.00, '2 วัน', 'active', NOW(), NOW()),
(3, 'แพ็คเกจพรีเมียม', 'บริการซ่อมและบำรุงรักษา', 3000.00, '3 วัน', 'active', NOW(), NOW());

-- ========================================
-- ตาราง 5: ACTIVITIES (ผลงาน)
-- ========================================
CREATE TABLE `activities` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text,
  `activity_date` date DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลตัวอย่าง ACTIVITIES
INSERT INTO `activities` (`id`, `title`, `description`, `activity_date`, `status`, `created_at`, `updated_at`) VALUES
(1, 'ซ่อมคอมพิวเตอร์ร้านค้า ABC', 'ซ่อมคอมพิวเตอร์เครื่องหลักของร้านค้า', '2025-07-25', 'active', NOW(), NOW()),
(2, 'ติดตั้งระบบ POS ร้านอาหาร', 'ติดตั้งระบบขายหน้าร้านสำหรับร้านอาหาร', '2025-07-26', 'active', NOW(), NOW()),
(3, 'บำรุงรักษาเครื่องคอมพิวเตอร์สำนักงาน', 'ทำความสะอาดและบำรุงรักษาเครื่องคอมพิวเตอร์', '2025-07-27', 'active', NOW(), NOW());

-- ========================================
-- ตาราง 6: ACTIVITY_IMAGES (รูปภาพผลงาน)
-- ========================================
CREATE TABLE `activity_images` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `activity_id` bigint(20) UNSIGNED NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `activity_images_activity_id_foreign` (`activity_id`),
  CONSTRAINT `activity_images_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลตัวอย่าง ACTIVITY_IMAGES
INSERT INTO `activity_images` (`id`, `activity_id`, `image_path`, `alt_text`, `description`, `created_at`, `updated_at`) VALUES
(1, 1, 'images/activities/repair-abc-before.jpg', 'ก่อนซ่อม', 'สภาพเครื่องก่อนซ่อม', NOW(), NOW()),
(2, 1, 'images/activities/repair-abc-after.jpg', 'หลังซ่อม', 'สภาพเครื่องหลังซ่อม', NOW(), NOW()),
(3, 2, 'images/activities/pos-install.jpg', 'ติดตั้ง POS', 'การติดตั้งระบบ POS', NOW(), NOW()),
(4, 3, 'images/activities/maintenance.jpg', 'บำรุงรักษา', 'การบำรุงรักษาเครื่อง', NOW(), NOW());

-- ========================================
-- ตาราง 7: BANNERS (แบนเนอร์)
-- ========================================
CREATE TABLE `banners` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `link_url` varchar(255) DEFAULT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลตัวอย่าง BANNERS
INSERT INTO `banners` (`id`, `title`, `image_path`, `link_url`, `alt_text`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
(1, 'โปรโมชั่นซ่อมคอมพิวเตอร์', 'images/banners/promo-repair.jpg', '/services', 'โปรโมชั่นซ่อม', 1, 'active', NOW(), NOW()),
(2, 'บริการติดตั้งซอฟต์แวร์', 'images/banners/software-banner.jpg', '/packages', 'ติดตั้งซอฟต์แวร์', 2, 'active', NOW(), NOW()),
(3, 'ผลงานการซ่อม', 'images/banners/portfolio-banner.jpg', '/activities', 'ผลงาน', 3, 'active', NOW(), NOW());

-- ========================================
-- ตาราง 8: CONTACTS (การติดต่อ)
-- ========================================
CREATE TABLE `contacts` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `status` enum('new','read','replied') DEFAULT 'new',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลตัวอย่าง CONTACTS
INSERT INTO `contacts` (`id`, `name`, `email`, `phone`, `subject`, `message`, `status`, `created_at`, `updated_at`) VALUES
(1, 'สมชาย ใจดี', '<EMAIL>', '************', 'สอบถามราคาซ่อมคอมพิวเตอร์', 'อยากทราบราคาซ่อมคอมพิวเตอร์เครื่องเก่า', 'new', NOW(), NOW()),
(2, 'สมหญิง รักงาน', '<EMAIL>', '************', 'ติดตั้งซอฟต์แวร์', 'ต้องการติดตั้งโปรแกรมบัญชี', 'read', NOW(), NOW()),
(3, 'วิชัย เก่งมาก', '<EMAIL>', '************', 'บำรุงรักษาเครื่อง', 'ต้องการบริการบำรุงรักษาประจำปี', 'replied', NOW(), NOW());

-- ========================================
-- ตาราง 9: SITE_SETTINGS (การตั้งค่า)
-- ========================================
CREATE TABLE `site_settings` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(255) NOT NULL,
  `setting_value` text,
  `setting_type` enum('text','number','boolean','json') DEFAULT 'text',
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `site_settings_setting_key_unique` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลตัวอย่าง SITE_SETTINGS
INSERT INTO `site_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'ผู้ใหญ่ประจักร์เซอร์วิสช็อป', 'text', 'ชื่อเว็บไซต์', NOW(), NOW()),
(2, 'site_description', 'บริการซ่อมคอมพิวเตอร์และติดตั้งซอฟต์แวร์', 'text', 'คำอธิบายเว็บไซต์', NOW(), NOW()),
(3, 'contact_phone', '************', 'text', 'เบอร์โทรติดต่อ', NOW(), NOW()),
(4, 'contact_email', '<EMAIL>', 'text', 'อีเมลติดต่อ', NOW(), NOW()),
(5, 'address', '123 ถนนหลัก ตำบลกลาง อำเภอเมือง จังหวัดกรุงเทพฯ 10000', 'text', 'ที่อยู่', NOW(), NOW()),
(6, 'facebook_url', 'https://facebook.com/prajakservice', 'text', 'Facebook URL', NOW(), NOW()),
(7, 'line_id', '@prajakservice', 'text', 'Line ID', NOW(), NOW()),
(8, 'business_hours', '08:00-18:00', 'text', 'เวลาทำการ', NOW(), NOW());

-- ========================================
-- ตาราง 10: MIGRATIONS (ระบบ Laravel)
-- ========================================
CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลตัวอย่าง MIGRATIONS
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '2014_10_12_000000_create_users_table', 1),
(2, '2014_10_12_100000_create_password_resets_table', 1),
(3, '2019_08_19_000000_create_failed_jobs_table', 1),
(4, '2025_07_31_000001_create_services_table', 2),
(5, '2025_07_31_000002_create_service_images_table', 2),
(6, '2025_07_31_000003_create_packages_table', 2),
(7, '2025_07_31_000004_create_activities_table', 2),
(8, '2025_07_31_000005_create_activity_images_table', 2),
(9, '2025_07_31_000006_create_banners_table', 2),
(10, '2025_07_31_000007_create_contacts_table', 2),
(11, '2025_07_31_000008_create_site_settings_table', 2);

-- ========================================
-- สิ้นสุดการสำรองข้อมูล
-- ========================================

-- เปิดใช้งาน Foreign Key Checks
SET FOREIGN_KEY_CHECKS = 1;

-- แสดงข้อความเสร็จสิ้น
SELECT 'Database backup completed successfully!' as Status;
