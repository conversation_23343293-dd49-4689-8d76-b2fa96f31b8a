# 🗂️ MIGRATIONS TABLE NOTEPAD - ตารางระบบ Laravel

## 📊 ข้อมูลพื้นฐาน

### 🎯 **วัตถุประสงค์**
ตาราง `migrations` เป็นตารางระบบของ Laravel ที่ใช้เก็บประวัติการสร้างและแก้ไขโครงสร้างฐานข้อมูล

### 📋 **โครงสร้างตาราง**
| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | `id` | INT | PK | รหัสลำดับการรัน (Auto Increment) |
| 2 | `migration` | VARCHAR(255) | - | ชื่อไฟล์ migration |
| 3 | `batch` | INT | - | กลุ่มการรัน migration |

### 🏗️ **SQL สร้างตาราง**
```sql
CREATE TABLE migrations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    migration VARCHAR(255) NOT NULL,
    batch INT NOT NULL
);
```

---

## 📈 ข้อมูลในตารางปัจจุบัน

### 🗃️ **รายการ Migration Files ทั้งหมด**

#### **Batch 1 - Laravel Default Tables**
```
id | migration                                           | batch
---|-----------------------------------------------------|-------
1  | 2014_10_12_000000_create_users_table               | 1
2  | 2014_10_12_100000_create_password_resets_table     | 1
3  | 2019_08_19_000000_create_failed_jobs_table         | 1
4  | 2019_12_14_000001_create_personal_access_tokens_table | 1
```

#### **Batch 2 - Core Application Tables**
```
id | migration                                           | batch
---|-----------------------------------------------------|-------
5  | 2025_07_16_181748_create_services_table            | 2
6  | 2025_07_16_181846_create_packages_table            | 2
7  | 2025_07_16_181924_create_contacts_table            | 2
8  | 2025_07_16_181949_create_site_settings_table       | 2
9  | 2025_07_16_184930_create_activities_table          | 2
```

#### **Batch 3 - Image Tables**
```
id | migration                                           | batch
---|-----------------------------------------------------|-------
10 | 2025_07_17_142814_create_activity_images_table     | 3
11 | 2025_07_17_152847_remove_price_from_services_and_packages_tables | 3
```

#### **Batch 4 - Background & Banner System**
```
id | migration                                           | batch
---|-----------------------------------------------------|-------
12 | 2025_07_19_100916_create_page_backgrounds_table    | 4
13 | 2025_07_19_103700_create_page_background_images_table | 4
14 | 2025_07_19_103737_add_slider_settings_to_page_backgrounds_table | 4
15 | 2025_07_19_104102_add_slider_fields_to_page_backgrounds_table | 4
16 | 2025_07_19_104157_update_background_type_enum_in_page_backgrounds_table | 4
17 | 2025_07_19_105450_add_fields_to_page_backgrounds_table | 4
18 | 2025_07_19_105537_add_fields_to_page_background_images_table | 4
19 | 2025_07_19_114857_create_banners_table             | 4
20 | 2025_07_19_140300_add_link_type_to_banners_table   | 4
21 | 2025_07_19_144340_add_page_filter_to_banners_table | 4
22 | 2025_07_19_151359_remove_link_fields_from_banners_table | 4
```

#### **Batch 5 - Final Updates**
```
id | migration                                           | batch
---|-----------------------------------------------------|-------
23 | 2025_07_20_140738_add_price_back_to_packages_table | 5
24 | 2025_07_20_145520_change_price_to_string_in_packages_table | 5
25 | 2025_07_20_171023_create_service_images_table      | 5
26 | 2025_07_20_180000_update_activity_images_table_structure | 5
27 | 2025_07_21_020902_make_image_nullable_in_activities_table | 5
```

---

## 🔧 คำสั่ง Artisan สำหรับ Migration

### 1. **ดูสถานะ Migration**
```bash
# ดูรายการ migration ทั้งหมด
php artisan migrate:status

# ผลลัพธ์:
# +------+--------------------------------------------------+-------+
# | Ran? | Migration                                        | Batch |
# +------+--------------------------------------------------+-------+
# | Yes  | 2014_10_12_000000_create_users_table            | 1     |
# | Yes  | 2025_07_16_181748_create_services_table         | 2     |
# +------+--------------------------------------------------+-------+
```

### 2. **รัน Migration**
```bash
# รัน migration ที่ยังไม่ได้รัน
php artisan migrate

# รัน migration แบบ force (ไม่ถามยืนยัน)
php artisan migrate --force

# รัน migration พร้อมแสดงรายละเอียด
php artisan migrate --verbose
```

### 3. **ย้อนกลับ Migration**
```bash
# ย้อนกลับ migration ล่าสุด (1 batch)
php artisan migrate:rollback

# ย้อนกลับ migration 3 batch ล่าสุด
php artisan migrate:rollback --step=3

# ย้อนกลับ migration ทั้งหมด
php artisan migrate:reset
```

### 4. **รีเฟรช Migration**
```bash
# ลบและสร้างใหม่ทั้งหมด
php artisan migrate:refresh

# รีเฟรชพร้อมรัน seeder
php artisan migrate:refresh --seed

# รีเฟรชเฉพาะ 3 batch ล่าสุด
php artisan migrate:refresh --step=3
```

### 5. **สร้าง Migration ใหม่**
```bash
# สร้าง migration สำหรับตารางใหม่
php artisan make:migration create_notes_table

# สร้าง migration สำหรับแก้ไขตาราง
php artisan make:migration add_column_to_services_table --table=services

# สร้าง migration พร้อม model
php artisan make:model Note -m
```

---

## 📊 การวิเคราะห์ข้อมูล

### 🔍 **SQL Queries สำหรับตรวจสอบ**

#### 1. ดูรายการ Migration ทั้งหมด
```sql
SELECT * FROM migrations ORDER BY id ASC;
```

#### 2. ดู Migration ตาม Batch
```sql
-- ดู migration ใน batch ล่าสุด
SELECT * FROM migrations WHERE batch = (SELECT MAX(batch) FROM migrations);

-- ดู migration ใน batch เฉพาะ
SELECT * FROM migrations WHERE batch = 2;
```

#### 3. นับจำนวน Migration
```sql
-- นับจำนวน migration ทั้งหมด
SELECT COUNT(*) as total_migrations FROM migrations;

-- นับจำนวน migration ตาม batch
SELECT batch, COUNT(*) as migration_count 
FROM migrations 
GROUP BY batch 
ORDER BY batch;
```

#### 4. ค้นหา Migration เฉพาะ
```sql
-- ค้นหา migration ที่มีคำว่า "services"
SELECT * FROM migrations WHERE migration LIKE '%services%';

-- ค้นหา migration ที่สร้างตาราง
SELECT * FROM migrations WHERE migration LIKE '%create_%';

-- ค้นหา migration ที่แก้ไขตาราง
SELECT * FROM migrations WHERE migration LIKE '%add_%' OR migration LIKE '%update_%';
```

---

## 📅 Timeline การพัฒนา

### **Phase 1: Laravel Setup (2014-2019)**
- สร้างตารางพื้นฐานของ Laravel
- ระบบ Authentication และ Jobs

### **Phase 2: Core Features (16 July 2025)**
- สร้างตารางหลัก: services, packages, activities
- ระบบการตั้งค่าและการติดต่อ

### **Phase 3: Image System (17 July 2025)**
- เพิ่มระบบรูปภาพสำหรับ activities
- ปรับปรุงโครงสร้างราคา

### **Phase 4: UI Enhancement (19 July 2025)**
- ระบบพื้นหลังและแบนเนอร์
- ปรับปรุงระบบลิงก์และการแสดงผล

### **Phase 5: Final Polish (20-21 July 2025)**
- เพิ่มระบบรูปภาพสำหรับ services
- ปรับปรุงโครงสร้างข้อมูลให้สมบูรณ์

---

## ⚠️ คำเตือนและข้อควรระวัง

### 🚨 **ห้ามทำ**
1. **ลบตาราง migrations** - จะทำให้ระบบ Laravel เสียหาย
2. **แก้ไขข้อมูลในตาราง** - อาจทำให้ migration ทำงานผิดพลาด
3. **รัน migration ในระบบจริงโดยไม่สำรอง** - อาจสูญเสียข้อมูล

### ✅ **ควรทำ**
1. **สำรองฐานข้อมูลก่อน migrate** - ป้องกันการสูญเสียข้อมูล
2. **ทดสอบใน development ก่อน** - ตรวจสอบความถูกต้อง
3. **ใช้ --pretend เพื่อดู SQL** - ตรวจสอบคำสั่งก่อนรัน

### 🔧 **Best Practices**
```bash
# ดู SQL ที่จะรันโดยไม่รันจริง
php artisan migrate --pretend

# สำรองฐานข้อมูลก่อน migrate
mysqldump -u username -p database_name > backup.sql

# รัน migration ใน production
php artisan migrate --force
```

---

## 🔍 การแก้ไขปัญหา

### **ปัญหาที่พบบ่อย**

#### 1. Migration ไม่รัน
```bash
# ตรวจสอบสถานะ
php artisan migrate:status

# ล้าง cache
php artisan config:clear
php artisan cache:clear
```

#### 2. Migration ขัดข้อง
```bash
# ย้อนกลับและรันใหม่
php artisan migrate:rollback
php artisan migrate
```

#### 3. ตาราง migrations หาย
```sql
-- สร้างตารางใหม่
CREATE TABLE migrations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    migration VARCHAR(255) NOT NULL,
    batch INT NOT NULL
);

-- เพิ่มข้อมูลที่รันแล้ว
INSERT INTO migrations (migration, batch) VALUES 
('2014_10_12_000000_create_users_table', 1),
('2025_07_16_181748_create_services_table', 2);
```

---

**📅 อัปเดตล่าสุด**: 31 กรกฎาคม 2025  
**👨‍💻 จัดทำโดย**: Augment Agent  
**🔧 Framework**: Laravel 9.x  
**⚠️ สำคัญ**: ตารางนี้เป็นหัวใจของระบบ Migration - ห้ามลบหรือแก้ไข!
