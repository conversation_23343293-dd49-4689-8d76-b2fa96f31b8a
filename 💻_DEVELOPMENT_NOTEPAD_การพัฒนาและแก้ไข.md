# 💻 DEVELOPMENT NOTEPAD - การพัฒนาและแก้ไข

## 🏗️ โครงสร้างโปรเจค Laravel

### 📁 โฟลเดอร์สำคัญ:
```
PhuyaiPrajakserviceshop/
├── app/
│   ├── Http/Controllers/     # Controllers
│   ├── Models/              # Models (Eloquent)
│   └── Providers/           # Service Providers
├── database/
│   ├── migrations/          # Database Migrations
│   └── seeders/            # Database Seeders
├── resources/
│   ├── views/              # Blade Templates
│   ├── css/                # CSS Files
│   └── js/                 # JavaScript Files
├── routes/
│   └── web.php             # Web Routes
├── public/
│   ├── css/                # Compiled CSS
│   ├── js/                 # Compiled JS
│   └── images/             # Static Images
└── storage/
    └── app/public/         # Uploaded Files
```

---

## 🎯 Models และ Relationships

### 1. 🛠️ Service Model
```php
// app/Models/Service.php
class Service extends Model
{
    protected $fillable = [
        'title', 'description', 'details', 'image', 
        'is_active', 'sort_order'
    ];

    // Relationship: 1 Service has many Images
    public function images()
    {
        return $this->hasMany(ServiceImage::class);
    }

    // Get cover image
    public function coverImage()
    {
        return $this->hasOne(ServiceImage::class)->where('is_cover', true);
    }

    // Scope: Active services only
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
```

### 2. 🖼️ ServiceImage Model
```php
// app/Models/ServiceImage.php
class ServiceImage extends Model
{
    protected $fillable = [
        'service_id', 'image_path', 'alt_text', 
        'description', 'is_cover', 'sort_order'
    ];

    // Relationship: Image belongs to Service
    public function service()
    {
        return $this->belongsTo(Service::class);
    }
}
```

### 3. 🎨 Activity Model
```php
// app/Models/Activity.php
class Activity extends Model
{
    protected $fillable = [
        'title', 'description', 'details', 
        'is_active', 'sort_order'
    ];

    // Relationship: 1 Activity has many Images
    public function images()
    {
        return $this->hasMany(ActivityImage::class);
    }

    // Get cover image
    public function coverImage()
    {
        return $this->hasOne(ActivityImage::class)->where('is_cover', true);
    }
}
```

---

## 🎮 Controllers และ Methods

### 1. 🏠 HomeController
```php
// app/Http/Controllers/HomeController.php
class HomeController extends Controller
{
    // หน้าแรก
    public function index()
    {
        $services = Service::active()->inRandomOrder()->limit(6)->get();
        $activities = Activity::active()->inRandomOrder()->limit(4)->get();
        $banners = Banner::active()
            ->whereJsonContains('display_pages', 'home')
            ->orderBy('sort_order')
            ->get();
        
        return view('home', compact('services', 'activities', 'banners'));
    }

    // หน้าบริการ
    public function services()
    {
        $services = Service::active()->orderBy('sort_order')->get();
        return view('services', compact('services'));
    }

    // รายละเอียดบริการ
    public function showService($id)
    {
        $service = Service::with('images')->findOrFail($id);
        return view('service-detail', compact('service'));
    }
}
```

### 2. 👨‍💼 AdminController
```php
// app/Http/Controllers/AdminController.php
class AdminController extends Controller
{
    // Dashboard
    public function dashboard()
    {
        $stats = [
            'services' => Service::count(),
            'activities' => Activity::count(),
            'packages' => Package::count(),
            'unread_contacts' => Contact::where('is_read', false)->count(),
        ];
        
        return view('admin.dashboard', compact('stats'));
    }

    // จัดการบริการ
    public function services()
    {
        $services = Service::orderBy('sort_order')->get();
        return view('admin.services.index', compact('services'));
    }

    // สร้างบริการใหม่
    public function storeService(Request $request)
    {
        $request->validate([
            'title' => 'required|max:255',
            'description' => 'required',
            'image' => 'image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $service = Service::create($request->all());
        
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('services', 'public');
            $service->update(['image' => $imagePath]);
        }

        return redirect()->route('admin.services')->with('success', 'เพิ่มบริการสำเร็จ');
    }
}
```

---

## 🛣️ Routes สำคัญ

### 1. Frontend Routes
```php
// routes/web.php

// หน้าหลัก
Route::get('/', [HomeController::class, 'index'])->name('home');

// บริการ
Route::get('/services', [HomeController::class, 'services'])->name('services');
Route::get('/services/{id}', [HomeController::class, 'showService'])->name('services.show');

// แพ็คเกจ
Route::get('/packages', [HomeController::class, 'packages'])->name('packages');
Route::get('/packages/{id}', [HomeController::class, 'showPackage'])->name('packages.show');

// ผลงาน
Route::get('/activities', [HomeController::class, 'activities'])->name('activities');
Route::get('/activities/{id}', [HomeController::class, 'showActivity'])->name('activities.show');

// ติดต่อ
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'storeContact'])->name('contact.store');
```

### 2. Admin Routes
```php
// Admin Routes (Protected by Auth Middleware)
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [AdminController::class, 'dashboard'])->name('index');
    
    // Services Management
    Route::get('/services', [AdminController::class, 'services'])->name('services');
    Route::get('/services/create', [AdminController::class, 'createService'])->name('services.create');
    Route::post('/services', [AdminController::class, 'storeService'])->name('services.store');
    Route::get('/services/{id}/edit', [AdminController::class, 'editService'])->name('services.edit');
    Route::put('/services/{id}', [AdminController::class, 'updateService'])->name('services.update');
    Route::delete('/services/{id}', [AdminController::class, 'deleteService'])->name('services.delete');
    
    // Image Management
    Route::post('/services/images/{id}/set-cover', [AdminController::class, 'setServiceImageCover'])->name('services.images.set-cover');
    Route::delete('/services/images/{id}', [AdminController::class, 'deleteServiceImage'])->name('services.images.delete');
});
```

---

## 🎨 Views และ Blade Templates

### 1. Layout หลัก
```php
<!-- resources/views/layouts/app.blade.php -->
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title') - {{ config('app.name') }}</title>
    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
</head>
<body>
    @include('partials.navbar')
    
    <main>
        @yield('content')
    </main>
    
    @include('partials.footer')
    
    <script src="{{ asset('js/app.js') }}"></script>
    @stack('scripts')
</body>
</html>
```

### 2. หน้าแรก
```php
<!-- resources/views/home.blade.php -->
@extends('layouts.app')

@section('title', 'หน้าแรก')

@section('content')
    <!-- Banner Slider -->
    @if($banners->count() > 0)
        <div class="banner-slider">
            @foreach($banners as $banner)
                <div class="banner-slide">
                    <img src="{{ asset('storage/' . $banner->image_path) }}" alt="{{ $banner->title }}">
                    <div class="banner-content">
                        <h2>{{ $banner->title }}</h2>
                        <p>{{ $banner->description }}</p>
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    <!-- Services Section -->
    <section class="services-section">
        <h2>บริการของเรา</h2>
        <div class="services-grid">
            @foreach($services as $service)
                <div class="service-card">
                    @if($service->coverImage)
                        <img src="{{ asset('storage/' . $service->coverImage->image_path) }}" alt="{{ $service->title }}">
                    @elseif($service->image)
                        <img src="{{ asset('storage/' . $service->image) }}" alt="{{ $service->title }}">
                    @endif
                    <h3>{{ $service->title }}</h3>
                    <p>{{ Str::limit($service->description, 100) }}</p>
                    <a href="{{ route('services.show', $service->id) }}" class="btn">ดูรายละเอียด</a>
                </div>
            @endforeach
        </div>
    </section>
@endsection
```

---

## 🗄️ Database Migrations

### 1. สร้าง Migration ใหม่
```bash
# สร้าง migration สำหรับตารางใหม่
php artisan make:migration create_notes_table

# สร้าง migration สำหรับแก้ไขตาราง
php artisan make:migration add_column_to_services_table

# รัน migration
php artisan migrate

# ย้อนกลับ migration
php artisan migrate:rollback
```

### 2. ตัวอย่าง Migration
```php
// database/migrations/xxxx_create_notes_table.php
public function up()
{
    Schema::create('notes', function (Blueprint $table) {
        $table->id();
        $table->string('title');
        $table->longText('content');
        $table->string('category')->nullable();
        $table->enum('priority', ['low', 'medium', 'high'])->default('medium');
        $table->boolean('is_pinned')->default(false);
        $table->timestamps();
    });
}
```

---

## 🎯 การจัดการไฟล์และรูปภาพ

### 1. Upload รูปภาพ
```php
// ใน Controller
public function uploadImage(Request $request)
{
    $request->validate([
        'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
    ]);

    if ($request->hasFile('image')) {
        // เก็บไฟล์ใน storage/app/public/services
        $imagePath = $request->file('image')->store('services', 'public');
        
        // บันทึกลงฐานข้อมูล
        ServiceImage::create([
            'service_id' => $request->service_id,
            'image_path' => $imagePath,
            'alt_text' => $request->alt_text,
            'description' => $request->description,
        ]);
    }
}
```

### 2. แสดงรูปภาพ
```php
<!-- ใน Blade Template -->
<img src="{{ asset('storage/' . $image->image_path) }}" alt="{{ $image->alt_text }}">

<!-- ตรวจสอบว่ามีรูปหรือไม่ -->
@if($service->coverImage)
    <img src="{{ asset('storage/' . $service->coverImage->image_path) }}" alt="{{ $service->title }}">
@else
    <img src="{{ asset('images/placeholder.jpg') }}" alt="No Image">
@endif
```

---

## 🔧 Artisan Commands ที่ใช้บ่อย

### 1. การจัดการ Cache
```bash
# ล้าง cache ทั้งหมด
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# สร้าง symbolic link สำหรับ storage
php artisan storage:link
```

### 2. การจัดการฐานข้อมูล
```bash
# รัน migration
php artisan migrate

# รัน seeder
php artisan db:seed

# รีเฟรชฐานข้อมูล
php artisan migrate:refresh --seed
```

### 3. การสร้างไฟล์
```bash
# สร้าง Controller
php artisan make:controller NoteController

# สร้าง Model
php artisan make:model Note -m

# สร้าง Migration
php artisan make:migration create_notes_table
```

---

## 🚀 การ Deploy และ Production

### 1. เตรียมสำหรับ Production
```bash
# Optimize สำหรับ production
php artisan config:cache
php artisan route:cache
php artisan view:cache

# สร้าง key สำหรับ application
php artisan key:generate
```

### 2. การตั้งค่า Environment
```env
# .env file
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

---

## 🐛 การ Debug และแก้ไขปัญหา

### 1. เปิด Debug Mode
```env
# ใน .env file
APP_DEBUG=true
```

### 2. ดู Log
```bash
# ดู log ล่าสุด
tail -f storage/logs/laravel.log

# ล้าง log
> storage/logs/laravel.log
```

### 3. ตรวจสอบ Route
```bash
# ดู route ทั้งหมด
php artisan route:list

# ดู route เฉพาะ
php artisan route:list --name=admin
```

---

**📅 อัปเดตล่าสุด**: 31 กรกฎาคม 2025
**👨‍💻 จัดทำโดย**: Augment Agent
**🔧 Framework**: Laravel 9.x
