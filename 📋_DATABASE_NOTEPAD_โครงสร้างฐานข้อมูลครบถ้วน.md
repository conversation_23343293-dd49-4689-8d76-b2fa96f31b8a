# 📋 DATABASE NOTEPAD - โครงสร้างฐานข้อมูลเว็บไซต์ผู้ใหญ่ประจักร์เซอร์วิสช็อป

## 📊 สรุปภาพรวม
- **ชื่อฐานข้อมูล**: phuyai_prajak_service_shop
- **จำนวนตารางทั้งหมด**: 16 ตาราง
- **ตารางที่ใช้งานจริง**: 11 ตาราง
- **ตารางที่ไม่ได้ใช้**: 5 ตาราง (Laravel default tables)

---

## 🗂️ รายการตารางทั้งหมด

### ✅ ตารางที่ใช้งานจริง (11 ตาราง)

#### 1. 👤 **users** - ผู้ดูแลระบบ (6 ฟิลด์)
| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสผู้ดูแลระบบ |
| 2 | name | VARCHAR(255) | - | ชื่อผู้ดูแลระบบ |
| 3 | email | VARCHAR(255) | UNIQUE | อีเมลสำหรับเข้าสู่ระบบ |
| 4 | password | VARCHAR(255) | - | รหัสผ่าน (เข้ารหัส) |
| 5 | created_at | TIMESTAMP | - | วันที่สร้างบัญชี |
| 6 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

#### 2. 🛠️ **services** - บริการ (8 ฟิลด์)
| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสบริการ |
| 2 | title | VARCHAR(255) | - | ชื่อบริการ |
| 3 | description | TEXT | - | คำอธิบายบริการ |
| 4 | details | TEXT | - | รายละเอียดเพิ่มเติม |
| 5 | image | VARCHAR(255) | - | รูปภาพหลักของบริการ |
| 6 | is_active | BOOLEAN | - | สถานะการใช้งาน |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

#### 3. 🖼️ **service_images** - รูปภาพบริการ (9 ฟิลด์)
| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสรูปภาพ |
| 2 | service_id | BIGINT | FK | รหัสบริการที่เป็นเจ้าของรูป |
| 3 | image_path | VARCHAR(255) | - | ที่อยู่ไฟล์รูปภาพ |
| 4 | alt_text | VARCHAR(255) | - | ข้อความทดแทนรูปภาพ |
| 5 | description | TEXT | - | คำอธิบายรูปภาพ |
| 6 | is_cover | BOOLEAN | - | รูปหน้าปก |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่อัปโหลด |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

#### 4. 📦 **packages** - แพ็คเกจบริการ (10 ฟิลด์)
| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสแพ็คเกจ |
| 2 | title | VARCHAR(255) | - | ชื่อแพ็คเกจ |
| 3 | description | TEXT | - | คำอธิบายแพ็คเกจ |
| 4 | details | TEXT | - | รายละเอียดเพิ่มเติม |
| 5 | price | VARCHAR(255) | - | ราคา (เก็บเป็น String) |
| 6 | image | VARCHAR(255) | - | รูปภาพแพ็คเกจ |
| 7 | is_active | BOOLEAN | - | สถานะการใช้งาน |
| 8 | sort_order | INT | - | ลำดับการแสดงผล |
| 9 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 10 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

#### 5. 🎨 **activities** - ผลงาน (9 ฟิลด์)
| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสผลงาน |
| 2 | title | VARCHAR(255) | - | ชื่อผลงาน |
| 3 | description | TEXT | - | คำอธิบายผลงาน |
| 4 | details | TEXT | - | รายละเอียดเพิ่มเติม |
| 5 | image | VARCHAR(255) | - | รูปภาพหลัก (nullable) |
| 6 | is_active | BOOLEAN | - | สถานะการแสดงผล |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

#### 6. 📸 **activity_images** - รูปภาพผลงาน (9 ฟิลด์)
| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสรูปภาพ |
| 2 | activity_id | BIGINT | FK | รหัสผลงานที่เป็นเจ้าของรูป |
| 3 | image_path | VARCHAR(255) | - | ที่อยู่ไฟล์รูปภาพ |
| 4 | alt_text | VARCHAR(255) | - | ข้อความทดแทนรูปภาพ |
| 5 | description | TEXT | - | คำอธิบายรูปภาพ |
| 6 | is_cover | BOOLEAN | - | รูปหน้าปก |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่อัปโหลด |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

#### 7. 🎯 **banners** - แบนเนอร์ (9 ฟิลด์)
| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสแบนเนอร์ |
| 2 | title | VARCHAR(255) | - | ชื่อแบนเนอร์ |
| 3 | description | TEXT | - | คำอธิบายแบนเนอร์ |
| 4 | image_path | VARCHAR(255) | - | ที่อยู่ไฟล์รูปภาพ |
| 5 | display_pages | JSON | - | หน้าที่ต้องการแสดง |
| 6 | is_active | BOOLEAN | - | สถานะการแสดงผล |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

#### 8. 📞 **contacts** - ข้อความติดต่อ (9 ฟิลด์)
| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสการติดต่อ |
| 2 | name | VARCHAR(255) | - | ชื่อผู้ติดต่อ |
| 3 | email | VARCHAR(255) | - | อีเมลผู้ติดต่อ |
| 4 | phone | VARCHAR(20) | - | เบอร์โทรศัพท์ |
| 5 | subject | VARCHAR(255) | - | หัวข้อการติดต่อ |
| 6 | message | TEXT | - | ข้อความ |
| 7 | is_read | BOOLEAN | - | สถานะการอ่าน |
| 8 | created_at | TIMESTAMP | - | วันที่ส่งข้อความ |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

#### 9. ⚙️ **site_settings** - การตั้งค่าเว็บไซต์ (6 ฟิลด์)
| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสการตั้งค่า |
| 2 | key | VARCHAR(255) | UNIQUE | คีย์การตั้งค่า |
| 3 | value | TEXT | - | ค่าการตั้งค่า |
| 4 | description | TEXT | - | คำอธิบายการตั้งค่า |
| 5 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 6 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

#### 10. 🎨 **page_backgrounds** - พื้นหลังหน้าเว็บ (หลายฟิลด์)
- ตารางสำหรับจัดการพื้นหลังของแต่ละหน้า
- รองรับทั้งรูปภาพและสไลด์โชว์

#### 11. 🖼️ **page_background_images** - รูปภาพพื้นหลัง (หลายฟิลด์)
- ตารางเก็บรูปภาพสำหรับพื้นหลัง
- เชื่อมโยงกับ page_backgrounds

---

## 🔗 ความสัมพันธ์ระหว่างตาราง

### One-to-Many Relationships:
1. **services** → **service_images** (1 บริการ : หลายรูป)
2. **activities** → **activity_images** (1 ผลงาน : หลายรูป)
3. **page_backgrounds** → **page_background_images** (1 พื้นหลัง : หลายรูป)

### Independent Tables:
- `users` - ตารางอิสระ
- `packages` - ตารางอิสระ
- `contacts` - ตารางอิสระ
- `banners` - ตารางอิสระ
- `site_settings` - ตารางอิสระ

---

## ❌ ตารางที่ไม่ได้ใช้งาน (5 ตาราง)

### Laravel Default Tables:
1. **password_resets** - รีเซ็ตรหัสผ่าน
2. **failed_jobs** - งานที่ล้มเหลว
3. **personal_access_tokens** - โทเค็นการเข้าถึง
4. **migrations** - ประวัติการสร้างตาราง (ตารางระบบ)

---

## 📈 สถิติการใช้งาน

### ตารางหลักที่ใช้ในหน้าเว็บ:
- **หน้าแรก**: services, activities, banners, site_settings
- **หน้าบริการ**: services, service_images
- **หน้าแพ็คเกจ**: packages
- **หน้าผลงาน**: activities, activity_images
- **หน้าติดต่อ**: contacts, site_settings
- **ระบบ Admin**: users, ทุกตาราง

### จำนวนฟิลด์รวม:
- **ตารางที่ใช้งาน**: ~80+ ฟิลด์
- **ตารางทั้งหมด**: ~100+ ฟิลด์

---

## 🔧 หมายเหตุสำคัญ

### การใช้งานข้อมูล:
1. **ตาราง services & activities** - เป็นหัวใจหลักของเว็บไซต์
2. **ตาราง *_images** - จัดการแกลเลอรี่รูปภาพ
3. **ตาราง site_settings** - ข้อมูลติดต่อและการตั้งค่า
4. **ตาราง banners** - สไลด์โชว์หน้าแรก

### การบำรุงรักษา:
- ตารางที่ไม่ได้ใช้สามารถลบได้เพื่อประหยัดพื้นที่
- ควรสำรองข้อมูลก่อนทำการแก้ไขโครงสร้าง
- ตาราง migrations ห้ามลบ (ตารางระบบ Laravel)

---

**📅 อัปเดตล่าสุด**: 31 กรกฎาคม 2025
**👨‍💻 จัดทำโดย**: Augment Agent
