# 📚 คู่มือแก้ไขแบบแยกหมวดหมู่ - ผู้ใหญ่ประจักร์ เซอร์วิส ช็อป

## 🎨 หมวดหมู่ที่ 1: การแก้ไขหน้าตาและธีม

### 🖼️ การเปลี่ยนโลโก้และรูปภาพ

#### 📍 เปลี่ยนโลโก้หลัก
**ไฟล์:** `resources/views/layouts/app.blade.php`
**บรรทัดที่:** 130
```html
<img src="{{ asset('images/โลโก้ผู้ใหญ่ประจักร์นกสีดำ.png') }}" alt="โลโก้หลัก" class="me-2" style="height: 60px; width: auto;">
```
**วิธีแก้:**
1. ใส่รูปโลโก้ใหม่ในโฟลเดอร์ `public/images/`
2. เปลี่ยนชื่อไฟล์ในโค้ด
3. ปรับขนาดใน style="height: XXpx;"

#### 🏠 เปลี่ยนรูป Favicon
**ไฟล์:** `public/favicon.ico`
**วิธีแก้:** แทนที่ไฟล์ favicon.ico ด้วยรูปใหม่ (ขนาด 16x16 หรือ 32x32 pixels)

#### 🎨 เปลี่ยนรูปแบนเนอร์หน้าหลัก
**วิธีที่ 1:** ผ่าน Admin Panel
1. เข้า `/admin/login`
2. ไปที่ "จัดการแบนเนอร์"
3. แก้ไขหรือเพิ่มแบนเนอร์ใหม่

**วิธีที่ 2:** แก้ไขในโค้ด
**ไฟล์:** `resources/views/frontend/home.blade.php`
**ค้นหา:** `@foreach($banners as $banner)`
**แก้ไข:** เปลี่ยน path รูปภาพ

### 🌈 การเปลี่ยนสีธีม

#### 🎨 เปลี่ยนสีหลักของเว็บไซต์
**ไฟล์:** `public/css/funeral-style.css`
**ค้นหา:** `:root` หรือ CSS Variables
```css
:root {
    --primary-color: #2c3e50;      /* สีหลัก - เปลี่ยนได้ */
    --secondary-color: #34495e;    /* สีรอง - เปลี่ยนได้ */
    --accent-color: #e74c3c;       /* สีเน้น - เปลี่ยนได้ */
    --text-color: #2c3e50;         /* สีข้อความ - เปลี่ยนได้ */
    --bg-color: #f8f9fa;           /* สีพื้นหลัง - เปลี่ยนได้ */
}
```

#### 🔘 เปลี่ยนสีปุ่ม
**ไฟล์:** `public/css/funeral-style.css`
```css
.btn-primary {
    background-color: #YOUR_COLOR;
    border-color: #YOUR_COLOR;
}

.btn-primary:hover {
    background-color: #DARKER_COLOR;
    border-color: #DARKER_COLOR;
}
```

#### 🧭 เปลี่ยนสี Navigation Bar
**ไฟล์:** `resources/views/layouts/app.blade.php`
**บรรทัดที่:** 125
```html
<!-- เปลี่ยน bg-white เป็นสีที่ต้องการ -->
<nav class="navbar navbar-expand-lg navbar-light bg-primary shadow-sm sticky-top">
```

### 📝 การเปลี่ยนฟอนต์

#### 🔤 เปลี่ยนฟอนต์หลัก
**ไฟล์:** `resources/css/app.css` หรือ `public/css/app.css`
```css
/* เพิ่ม Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap');

body {
    font-family: 'Sarabun', 'Kanit', sans-serif;
}
```

#### 📏 เปลี่ยนขนาดฟอนต์
**ไฟล์:** `public/css/funeral-style.css`
```css
/* ฟอนต์หัวข้อ */
h1, h2, h3, h4, h5, h6 {
    font-size: 1.2em; /* ปรับขนาดตามต้องการ */
}

/* ฟอนต์เนื้อหา */
p, span, div {
    font-size: 1em; /* ปรับขนาดตามต้องการ */
}
```

---

## 📝 หมวดหมู่ที่ 2: การแก้ไขเนื้อหาและข้อความ

### 🏠 การแก้ไขหน้าหลัก

#### 📢 เปลี่ยนข้อความ Hero Section
**ไฟล์:** `resources/views/frontend/home.blade.php`
**ค้นหา:** `<div class="hero-section">`
**ตัวอย่างการแก้ไข:**
```html
<h1 class="display-4 fw-bold mb-4">ข้อความหัวข้อใหม่</h1>
<p class="lead mb-4">ข้อความคำอธิบายใหม่</p>
<a href="#" class="btn btn-primary btn-lg">ปุ่มใหม่</a>
```

#### 🏢 เปลี่ยนข้อมูลบริษัท
**ไฟล์:** `resources/views/frontend/home.blade.php`
**ค้นหา:** `<section class="about-section">`
**แก้ไข:** ข้อความในส่วนเกี่ยวกับเรา

#### 📞 เปลี่ยนข้อมูลติดต่อ
**วิธีที่ 1:** ผ่าน Admin Panel
1. เข้า `/admin/settings`
2. แก้ไขข้อมูลติดต่อ
3. บันทึก

**วิธีที่ 2:** แก้ไขในฐานข้อมูล
**ตาราง:** `site_settings`
**ฟิลด์สำคัญ:**
- `contact_phone` - เบอร์โทรศัพท์
- `contact_email` - อีเมล
- `contact_address` - ที่อยู่
- `contact_line` - Line ID
- `contact_facebook` - Facebook

### 🧭 การแก้ไขเมนูนำทาง

#### 📋 เพิ่มเมนูใหม่
**ไฟล์:** `resources/views/layouts/app.blade.php`
**บรรทัดที่:** 139-155
```html
<ul class="navbar-nav ms-auto">
    <li class="nav-item">
        <a class="nav-link" href="{{ route('home') }}">หน้าหลัก</a>
    </li>
    <!-- เพิ่มเมนูใหม่ที่นี่ -->
    <li class="nav-item">
        <a class="nav-link" href="/หน้าใหม่">เมนูใหม่</a>
    </li>
</ul>
```

#### 🔄 เปลี่ยนลำดับเมนู
**วิธี:** ย้ายตำแหน่ง `<li class="nav-item">` ในไฟล์เดียวกัน

#### 🚫 ซ่อนเมนู
**วิธี:** เพิ่ม `style="display: none;"` หรือลบ `<li>` ทั้งหมด

### 📄 การแก้ไขหน้าอื่นๆ

#### 🛠️ หน้าบริการ
**ไฟล์:** `resources/views/frontend/services.blade.php`
**ส่วนที่แก้ไขได้:**
- หัวข้อหน้า
- คำอธิบายบริการ
- ข้อความ Call-to-Action

#### 📦 หน้าแพ็คเกจ
**ไฟล์:** `resources/views/frontend/packages.blade.php`
**ส่วนที่แก้ไขได้:**
- หัวข้อแพ็คเกจ
- คำอธิบายแพ็คเกจ
- ข้อความราคา

#### 🎨 หน้าผลงาน
**ไฟล์:** `resources/views/frontend/activities.blade.php`
**ส่วนที่แก้ไขได้:**
- หัวข้อผลงาน
- คำอธิบายผลงาน
- ข้อมูลวันที่และสถานที่

---

## 🛠️ หมวดหมู่ที่ 3: การจัดการข้อมูลผ่าน Admin

### 🔐 การเข้าสู่ระบบ Admin

#### 🚪 หน้าล็อกอิน
**URL:** `/admin/login`
**ไฟล์:** `resources/views/admin/login.blade.php`
**ข้อมูลเข้าสู่ระบบ:** ตรวจสอบในตาราง `users`

#### 👤 การสร้าง Admin ใหม่
**วิธีที่ 1:** ผ่าน Database
```sql
INSERT INTO users (name, email, password, role, created_at, updated_at) 
VALUES ('ชื่อ Admin', '<EMAIL>', '$2y$10$...', 'admin', NOW(), NOW());
```

**วิธีที่ 2:** ผ่าน Laravel Tinker
```php
php artisan tinker
User::create([
    'name' => 'ชื่อ Admin',
    'email' => '<EMAIL>',
    'password' => bcrypt('รหัสผ่าน'),
    'role' => 'admin'
]);
```

### 📊 การจัดการเนื้อหาผ่าน Admin

#### 🛠️ จัดการบริการ
**URL:** `/admin/services`
**ฟีเจอร์:**
- ✅ เพิ่มบริการใหม่
- ✏️ แก้ไขบริการ
- 🗑️ ลบบริการ
- 📸 อัปโหลดรูปภาพ
- 🔢 จัดลำดับการแสดง
- 👁️ เปิด/ปิดการแสดงผล

#### 📦 จัดการแพ็คเกจ
**URL:** `/admin/packages`
**ฟีเจอร์:**
- ✅ เพิ่มแพ็คเกจใหม่
- ✏️ แก้ไขแพ็คเกจ
- 💰 กำหนดราคา
- ⭐ ตั้งเป็นแพ็คเกจแนะนำ
- 📋 จัดการคุณสมบัติ

#### 🎨 จัดการผลงาน
**URL:** `/admin/activities`
**ฟีเจอร์:**
- ✅ เพิ่มผลงานใหม่
- 📸 อัปโหลดรูปภาพหลายรูป
- 📅 กำหนดวันที่จัดงาน
- 📍 ระบุสถานที่
- 📝 เขียนรายละเอียด

#### 🖼️ จัดการแบนเนอร์
**URL:** `/admin/banners`
**ฟีเจอร์:**
- ✅ เพิ่มแบนเนอร์ใหม่
- 🔗 กำหนดลิงก์
- 📱 เลือกหน้าที่แสดง
- 🔢 จัดลำดับ
- 👁️ เปิด/ปิดการแสดงผล

### ⚙️ การตั้งค่าเว็บไซต์

#### 🏢 ข้อมูลพื้นฐาน
**URL:** `/admin/settings`
**ข้อมูลที่แก้ไขได้:**
- 🏷️ ชื่อเว็บไซต์
- 📝 คำอธิบายเว็บไซต์
- 📞 เบอร์โทรศัพท์
- 📧 อีเมล
- 🏠 ที่อยู่
- 💬 Line ID
- 📘 Facebook

#### 🌐 โซเชียลมีเดีย
**ฟิลด์ในฐานข้อมูล:**
- `social_facebook`
- `social_line`
- `social_instagram`
- `social_youtube`

---

## 🗃️ หมวดหมู่ที่ 4: การจัดการฐานข้อมูล

### 📊 ตารางสำคัญและการใช้งาน

#### 👥 ตาราง users (ผู้ใช้งาน)
**ฟิลด์สำคัญ:**
- `id` - รหัสผู้ใช้
- `name` - ชื่อผู้ใช้
- `email` - อีเมล (ใช้ล็อกอิน)
- `password` - รหัสผ่าน (เข้ารหัส)
- `role` - บทบาท (admin, user)

**การแก้ไข:**
```sql
-- เปลี่ยนรหัสผ่าน
UPDATE users SET password = '$2y$10$...' WHERE email = '<EMAIL>';

-- เปลี่ยนชื่อ
UPDATE users SET name = 'ชื่อใหม่' WHERE id = 1;
```

#### 🛠️ ตาราง services (บริการ)
**ฟิลด์สำคัญ:**
- `title` - ชื่อบริการ
- `description` - คำอธิบายสั้น
- `details` - รายละเอียดเต็ม
- `image` - รูปภาพหลัก
- `is_active` - สถานะ (1=แสดง, 0=ซ่อน)
- `sort_order` - ลำดับการแสดง

**การแก้ไข:**
```sql
-- เปลี่ยนชื่อบริการ
UPDATE services SET title = 'ชื่อบริการใหม่' WHERE id = 1;

-- เปิด/ปิดการแสดงผล
UPDATE services SET is_active = 1 WHERE id = 1;
```

#### 📦 ตาราง packages (แพ็คเกจ)
**ฟิลด์สำคัญ:**
- `name` - ชื่อแพ็คเกจ
- `description` - คำอธิบาย
- `features` - คุณสมบัติ (JSON)
- `price_text` - ข้อความราคา
- `is_featured` - แพ็คเกจแนะนำ

#### 🎨 ตาราง activities (ผลงาน)
**ฟิลด์สำคัญ:**
- `title` - ชื่อผลงาน
- `description` - คำอธิบาย
- `activity_date` - วันที่จัดงาน
- `location` - สถานที่

#### 🖼️ ตาราง banners (แบนเนอร์)
**ฟิลด์สำคัญ:**
- `title` - ชื่อแบนเนอร์
- `image` - รูปภาพ
- `link_url` - ลิงก์
- `page_location` - หน้าที่แสดง

#### ⚙️ ตาราง site_settings (การตั้งค่า)
**ฟิลด์สำคัญ:**
- `site_name` - ชื่อเว็บไซต์
- `site_description` - คำอธิบาย
- `contact_phone` - เบอร์โทร
- `contact_email` - อีเมล
- `contact_address` - ที่อยู่

### 💾 การ Backup และ Restore

#### 📤 การ Backup
```bash
# Backup ฐานข้อมูลทั้งหมด
mysqldump -u username -p phuyai_prajak_service_shop > backup_$(date +%Y%m%d_%H%M%S).sql

# Backup เฉพาะตารางสำคัญ
mysqldump -u username -p phuyai_prajak_service_shop users services packages activities banners site_settings > backup_important_$(date +%Y%m%d).sql
```

#### 📥 การ Restore
```bash
# Restore ฐานข้อมูล
mysql -u username -p phuyai_prajak_service_shop < backup_file.sql
```

#### 🔄 การ Export/Import ข้อมูล
```sql
-- Export ข้อมูลเป็น CSV
SELECT * FROM services INTO OUTFILE '/path/to/services.csv' 
FIELDS TERMINATED BY ',' 
ENCLOSED BY '"' 
LINES TERMINATED BY '\n';

-- Import ข้อมูลจาก CSV
LOAD DATA INFILE '/path/to/services.csv' 
INTO TABLE services 
FIELDS TERMINATED BY ',' 
ENCLOSED BY '"' 
LINES TERMINATED BY '\n';
```

---

*📝 คู่มือนี้แยกหมวดหมู่การแก้ไขอย่างละเอียด เพื่อให้ง่ายต่อการค้นหาและใช้งาน*
