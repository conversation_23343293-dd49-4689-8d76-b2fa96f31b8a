# 🔧 SQL QUERIES NOTEPAD - คำสั่งที่ใช้บ่อย

## 📊 คำสั่ง SELECT พื้นฐาน

### 1. 🛠️ ดูข้อมูลบริการทั้งหมด
```sql
-- ดูบริการที่เปิดใช้งาน
SELECT * FROM services WHERE is_active = 1 ORDER BY sort_order ASC;

-- ดูบริการพร้อมจำนวนรูปภาพ
SELECT s.*, COUNT(si.id) as image_count 
FROM services s 
LEFT JOIN service_images si ON s.id = si.service_id 
WHERE s.is_active = 1 
GROUP BY s.id 
ORDER BY s.sort_order ASC;

-- ดูบริการที่มีรูปหน้าปก
SELECT s.*, si.image_path as cover_image 
FROM services s 
LEFT JOIN service_images si ON s.id = si.service_id AND si.is_cover = 1 
WHERE s.is_active = 1 
ORDER BY s.sort_order ASC;
```

### 2. 🎨 ดูข้อมูลผลงานทั้งหมด
```sql
-- ดูผลงานที่เปิดใช้งาน
SELECT * FROM activities WHERE is_active = 1 ORDER BY sort_order ASC;

-- ดูผลงานพร้อมรูปหน้าปก
SELECT a.*, ai.image_path as cover_image 
FROM activities a 
LEFT JOIN activity_images ai ON a.id = ai.activity_id AND ai.is_cover = 1 
WHERE a.is_active = 1 
ORDER BY a.sort_order ASC;

-- ดูผลงาน 4 รายการแบบสุ่ม (สำหรับหน้าแรก)
SELECT a.*, ai.image_path as cover_image 
FROM activities a 
LEFT JOIN activity_images ai ON a.id = ai.activity_id AND ai.is_cover = 1 
WHERE a.is_active = 1 
ORDER BY RAND() 
LIMIT 4;
```

### 3. 📦 ดูข้อมูลแพ็คเกจ
```sql
-- ดูแพ็คเกจทั้งหมด
SELECT * FROM packages WHERE is_active = 1 ORDER BY sort_order ASC;

-- ดูแพ็คเกจที่มีราคา
SELECT * FROM packages WHERE is_active = 1 AND price IS NOT NULL ORDER BY sort_order ASC;
```

### 4. 🎯 ดูข้อมูลแบนเนอร์
```sql
-- ดูแบนเนอร์สำหรับหน้าแรก
SELECT * FROM banners 
WHERE is_active = 1 
AND JSON_CONTAINS(display_pages, '"home"') 
ORDER BY sort_order ASC;

-- ดูแบนเนอร์สำหรับหน้าบริการ
SELECT * FROM banners 
WHERE is_active = 1 
AND JSON_CONTAINS(display_pages, '"services"') 
ORDER BY sort_order ASC;
```

### 5. 📞 ดูข้อมูลการติดต่อ
```sql
-- ดูข้อความที่ยังไม่อ่าน
SELECT * FROM contacts WHERE is_read = 0 ORDER BY created_at DESC;

-- ดูข้อความทั้งหมดล่าสุด
SELECT * FROM contacts ORDER BY created_at DESC LIMIT 10;

-- นับจำนวนข้อความที่ยังไม่อ่าน
SELECT COUNT(*) as unread_count FROM contacts WHERE is_read = 0;
```

### 6. ⚙️ ดูการตั้งค่าเว็บไซต์
```sql
-- ดูการตั้งค่าทั้งหมด
SELECT * FROM site_settings ORDER BY key ASC;

-- ดูการตั้งค่าเฉพาะ
SELECT value FROM site_settings WHERE key = 'site_name';
SELECT value FROM site_settings WHERE key = 'phone';
SELECT value FROM site_settings WHERE key = 'email';
```

---

## 🔄 คำสั่ง INSERT พื้นฐาน

### 1. เพิ่มบริการใหม่
```sql
INSERT INTO services (title, description, details, image, is_active, sort_order) 
VALUES ('ชื่อบริการ', 'คำอธิบายสั้น', 'รายละเอียดยาว', 'path/to/image.jpg', 1, 1);
```

### 2. เพิ่มรูปภาพบริการ
```sql
INSERT INTO service_images (service_id, image_path, alt_text, description, is_cover, sort_order) 
VALUES (1, 'path/to/image.jpg', 'ข้อความทดแทน', 'คำอธิบายรูป', 0, 1);
```

### 3. เพิ่มผลงานใหม่
```sql
INSERT INTO activities (title, description, details, is_active, sort_order) 
VALUES ('ชื่อผลงาน', 'คำอธิบายสั้น', 'รายละเอียดยาว', 1, 1);
```

### 4. เพิ่มแพ็คเกจใหม่
```sql
INSERT INTO packages (title, description, details, price, image, is_active, sort_order) 
VALUES ('ชื่อแพ็คเกจ', 'คำอธิบาย', 'รายละเอียด', '50,000 บาท', 'path/to/image.jpg', 1, 1);
```

### 5. เพิ่มแบนเนอร์ใหม่
```sql
INSERT INTO banners (title, description, image_path, display_pages, is_active, sort_order) 
VALUES ('ชื่อแบนเนอร์', 'คำอธิบาย', 'path/to/banner.jpg', '["home", "services"]', 1, 1);
```

---

## ✏️ คำสั่ง UPDATE พื้นฐาน

### 1. อัปเดตสถานะการใช้งาน
```sql
-- เปิด/ปิดบริการ
UPDATE services SET is_active = 0 WHERE id = 1;
UPDATE services SET is_active = 1 WHERE id = 1;

-- เปิด/ปิดผลงาน
UPDATE activities SET is_active = 0 WHERE id = 1;

-- เปิด/ปิดแพ็คเกจ
UPDATE packages SET is_active = 0 WHERE id = 1;
```

### 2. อัปเดตลำดับการแสดงผล
```sql
-- เปลี่ยนลำดับบริการ
UPDATE services SET sort_order = 1 WHERE id = 1;
UPDATE services SET sort_order = 2 WHERE id = 2;

-- เปลี่ยนลำดับผลงาน
UPDATE activities SET sort_order = 1 WHERE id = 1;
```

### 3. ตั้งรูปหน้าปก
```sql
-- ยกเลิกรูปหน้าปกเดิม
UPDATE service_images SET is_cover = 0 WHERE service_id = 1;
-- ตั้งรูปหน้าปกใหม่
UPDATE service_images SET is_cover = 1 WHERE id = 5;

-- สำหรับผลงาน
UPDATE activity_images SET is_cover = 0 WHERE activity_id = 1;
UPDATE activity_images SET is_cover = 1 WHERE id = 10;
```

### 4. ทำเครื่องหมายข้อความอ่านแล้ว
```sql
UPDATE contacts SET is_read = 1 WHERE id = 1;
-- หรืออ่านทั้งหมด
UPDATE contacts SET is_read = 1 WHERE is_read = 0;
```

### 5. อัปเดตการตั้งค่าเว็บไซต์
```sql
UPDATE site_settings SET value = 'ชื่อเว็บไซต์ใหม่' WHERE key = 'site_name';
UPDATE site_settings SET value = '02-123-4567' WHERE key = 'phone';
UPDATE site_settings SET value = '<EMAIL>' WHERE key = 'email';
```

---

## 🗑️ คำสั่ง DELETE พื้นฐาน

### 1. ลบข้อมูล (ระวัง!)
```sql
-- ลบบริการ (จะลบรูปภาพด้วยเพราะมี CASCADE)
DELETE FROM services WHERE id = 1;

-- ลบรูปภาพเฉพาะ
DELETE FROM service_images WHERE id = 1;

-- ลบผลงาน
DELETE FROM activities WHERE id = 1;

-- ลบข้อความติดต่อ
DELETE FROM contacts WHERE id = 1;
```

---

## 📊 คำสั่งสถิติและรายงาน

### 1. สถิติทั่วไป
```sql
-- นับจำนวนบริการ
SELECT COUNT(*) as total_services FROM services WHERE is_active = 1;

-- นับจำนวนผลงาน
SELECT COUNT(*) as total_activities FROM activities WHERE is_active = 1;

-- นับจำนวนแพ็คเกจ
SELECT COUNT(*) as total_packages FROM packages WHERE is_active = 1;

-- นับจำนวนข้อความติดต่อ
SELECT COUNT(*) as total_contacts FROM contacts;
SELECT COUNT(*) as unread_contacts FROM contacts WHERE is_read = 0;
```

### 2. รายงานรูปภาพ
```sql
-- บริการที่ไม่มีรูปภาพ
SELECT s.* FROM services s 
LEFT JOIN service_images si ON s.id = si.service_id 
WHERE si.id IS NULL AND s.is_active = 1;

-- ผลงานที่ไม่มีรูปหน้าปก
SELECT a.* FROM activities a 
LEFT JOIN activity_images ai ON a.id = ai.activity_id AND ai.is_cover = 1 
WHERE ai.id IS NULL AND a.is_active = 1;
```

### 3. รายงานการใช้งาน
```sql
-- ข้อความติดต่อรายเดือน
SELECT 
    YEAR(created_at) as year, 
    MONTH(created_at) as month, 
    COUNT(*) as contact_count 
FROM contacts 
GROUP BY YEAR(created_at), MONTH(created_at) 
ORDER BY year DESC, month DESC;

-- บริการที่ถูกดูมากที่สุด (ถ้ามีระบบ tracking)
SELECT * FROM services WHERE is_active = 1 ORDER BY sort_order ASC;
```

---

## 🔧 คำสั่งบำรุงรักษา

### 1. ตรวจสอบความสมบูรณ์ข้อมูล
```sql
-- ตรวจสอบบริการที่ไม่มีรูปภาพ
SELECT s.id, s.title, COUNT(si.id) as image_count 
FROM services s 
LEFT JOIN service_images si ON s.id = si.service_id 
GROUP BY s.id 
HAVING image_count = 0;

-- ตรวจสอบรูปภาพที่ไม่มีเจ้าของ
SELECT * FROM service_images si 
LEFT JOIN services s ON si.service_id = s.id 
WHERE s.id IS NULL;
```

### 2. ทำความสะอาดข้อมูล
```sql
-- ลบข้อความติดต่อเก่า (เก่ากว่า 1 ปี)
DELETE FROM contacts WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- รีเซ็ตลำดับการแสดงผล
SET @row_number = 0;
UPDATE services SET sort_order = (@row_number:=@row_number + 1) WHERE is_active = 1 ORDER BY sort_order ASC;
```

---

## 🚨 คำสั่งฉุกเฉิน

### 1. สำรองข้อมูล
```sql
-- Export ข้อมูลสำคัญ
SELECT * FROM services INTO OUTFILE '/tmp/services_backup.csv';
SELECT * FROM activities INTO OUTFILE '/tmp/activities_backup.csv';
```

### 2. กู้คืนข้อมูล
```sql
-- ปิดการใช้งานทั้งหมดก่อน
UPDATE services SET is_active = 0;
UPDATE activities SET is_active = 0;
UPDATE packages SET is_active = 0;
UPDATE banners SET is_active = 0;
```

---

**📅 อัปเดตล่าสุด**: 31 กรกฎาคม 2025
**👨‍💻 จัดทำโดย**: Augment Agent
**⚠️ คำเตือน**: ทดสอบคำสั่งในฐานข้อมูลทดสอบก่อนใช้งานจริง
