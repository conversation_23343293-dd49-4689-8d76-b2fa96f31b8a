<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-R Diagram แบบมาตรฐาน - ระบบฐานข้อมูลเว็บไซต์ผู้ใหญ่ประจักร์ เซอร์วิส ช็อป</title>
    <style>
        body {
            font-family: 'Sarabun', 'TH Sarabun New', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 18px;
            opacity: 0.9;
        }
        
        .diagram-container {
            padding: 40px;
            background: #f8f9fa;
        }
        
        .legend {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .legend h3 {
            color: #667eea;
            margin: 0 0 15px 0;
            font-size: 20px;
        }
        
        .legend-items {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .legend-shape {
            width: 40px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
        }
        
        .entity-shape {
            background: #2196F3;
            border-radius: 3px;
        }
        
        .relationship-shape {
            background: #9C27B0;
            transform: rotate(45deg);
            border-radius: 3px;
        }
        
        .attribute-shape {
            background: #FF9800;
            border-radius: 50%;
        }
        
        .diagram-svg {
            width: 100%;
            height: 800px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .entity {
            fill: #e3f2fd;
            stroke: #1976d2;
            stroke-width: 2;
        }
        
        .entity-text {
            fill: #1976d2;
            font-size: 14px;
            font-weight: bold;
            text-anchor: middle;
            dominant-baseline: middle;
        }
        
        .relationship {
            fill: #f3e5f5;
            stroke: #7b1fa2;
            stroke-width: 2;
        }
        
        .relationship-text {
            fill: #7b1fa2;
            font-size: 12px;
            font-weight: bold;
            text-anchor: middle;
            dominant-baseline: middle;
        }
        
        .attribute {
            fill: #fff3e0;
            stroke: #f57c00;
            stroke-width: 1.5;
        }
        
        .attribute-text {
            fill: #f57c00;
            font-size: 11px;
            text-anchor: middle;
            dominant-baseline: middle;
        }
        
        .connection {
            stroke: #666;
            stroke-width: 1.5;
            fill: none;
        }
        
        .cardinality {
            fill: #333;
            font-size: 12px;
            font-weight: bold;
        }
        
        .summary {
            background: white;
            padding: 30px;
            margin-top: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .summary h3 {
            color: #667eea;
            margin: 0 0 20px 0;
            font-size: 22px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .summary-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .summary-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .summary-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .summary-item li {
            margin: 5px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>E-R Diagram แบบมาตรฐาน</h1>
            <h2>ระบบฐานข้อมูลเว็บไซต์ผู้ใหญ่ประจักร์ เซอร์วิส ช็อป</h2>
            <p>วันที่จัดทำ: 21 กรกฎาคม 2568</p>
        </div>
        
        <div class="diagram-container">
            <div class="legend">
                <h3>🔍 คำอธิบายสัญลักษณ์</h3>
                <div class="legend-items">
                    <div class="legend-item">
                        <div class="legend-shape entity-shape">E</div>
                        <span><strong>สี่เหลี่ยมผืนผ้า</strong> = Entity (ตาราง/เอนทิตี)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-shape relationship-shape">R</div>
                        <span><strong>รูปข้าวหลามตัด</strong> = Relationship (ความสัมพันธ์)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-shape attribute-shape">A</div>
                        <span><strong>วงรี</strong> = Attribute (คุณลักษณะ/ฟิลด์)</span>
                    </div>
                </div>
            </div>
            
            <svg class="diagram-svg" viewBox="0 0 1200 700">
                <!-- Users Entity -->
                <rect x="50" y="50" width="120" height="60" class="entity"/>
                <text x="110" y="85" class="entity-text">ผู้ดูแลระบบ</text>
                
                <!-- Users Attributes -->
                <ellipse cx="30" cy="30" rx="25" ry="15" class="attribute"/>
                <text x="30" y="30" class="attribute-text">รหัสผู้ดูแล</text>
                <line x1="55" y1="45" x2="50" y2="50" class="connection"/>
                
                <ellipse cx="110" cy="20" rx="20" ry="12" class="attribute"/>
                <text x="110" y="20" class="attribute-text">ชื่อ</text>
                <line x1="110" y1="32" x2="110" y2="50" class="connection"/>
                
                <ellipse cx="190" cy="30" rx="20" ry="12" class="attribute"/>
                <text x="190" y="30" class="attribute-text">อีเมล</text>
                <line x1="170" y1="42" x2="170" y2="50" class="connection"/>
                
                <!-- Services Entity -->
                <rect x="300" y="200" width="100" height="50" class="entity"/>
                <text x="350" y="230" class="entity-text">บริการ</text>
                
                <!-- Services Attributes -->
                <ellipse cx="250" cy="180" rx="25" ry="12" class="attribute"/>
                <text x="250" y="180" class="attribute-text">รหัสบริการ</text>
                <line x1="275" y1="188" x2="300" y2="210" class="connection"/>
                
                <ellipse cx="350" cy="160" rx="25" ry="12" class="attribute"/>
                <text x="350" y="160" class="attribute-text">ชื่อบริการ</text>
                <line x1="350" y1="172" x2="350" y2="200" class="connection"/>
                
                <ellipse cx="450" cy="180" rx="25" ry="12" class="attribute"/>
                <text x="450" y="180" class="attribute-text">คำอธิบาย</text>
                <line x1="425" y1="188" x2="400" y2="210" class="connection"/>
                
                <!-- Service Images Entity -->
                <rect x="500" y="300" width="120" height="50" class="entity"/>
                <text x="560" y="330" class="entity-text">รูปภาพบริการ</text>
                
                <!-- Service Images Attributes -->
                <ellipse cx="450" cy="280" rx="30" ry="12" class="attribute"/>
                <text x="450" y="280" class="attribute-text">รหัสรูปภาพ</text>
                <line x1="480" y1="288" x2="500" y2="310" class="connection"/>
                
                <ellipse cx="560" cy="260" rx="25" ry="12" class="attribute"/>
                <text x="560" y="260" class="attribute-text">ที่อยู่ไฟล์</text>
                <line x1="560" y1="272" x2="560" y2="300" class="connection"/>
                
                <!-- Packages Entity -->
                <rect x="700" y="50" width="100" height="50" class="entity"/>
                <text x="750" y="80" class="entity-text">แพ็คเกจ</text>
                
                <!-- Packages Attributes -->
                <ellipse cx="650" cy="30" rx="25" ry="12" class="attribute"/>
                <text x="650" y="30" class="attribute-text">รหัสแพ็คเกจ</text>
                <line x1="675" y1="38" x2="700" y2="60" class="connection"/>
                
                <ellipse cx="750" cy="20" rx="25" ry="12" class="attribute"/>
                <text x="750" y="20" class="attribute-text">ชื่อแพ็คเกจ</text>
                <line x1="750" y1="32" x2="750" y2="50" class="connection"/>
                
                <ellipse cx="850" cy="30" rx="20" ry="12" class="attribute"/>
                <text x="850" y="30" class="attribute-text">ราคา</text>
                <line x1="825" y1="38" x2="800" y2="60" class="connection"/>
                
                <!-- Activities Entity -->
                <rect x="900" y="200" width="100" height="50" class="entity"/>
                <text x="950" y="230" class="entity-text">ผลงาน</text>
                
                <!-- Activities Attributes -->
                <ellipse cx="850" cy="180" rx="25" ry="12" class="attribute"/>
                <text x="850" y="180" class="attribute-text">รหัสผลงาน</text>
                <line x1="875" y1="188" x2="900" y2="210" class="connection"/>
                
                <ellipse cx="950" cy="160" rx="25" ry="12" class="attribute"/>
                <text x="950" y="160" class="attribute-text">ชื่อผลงาน</text>
                <line x1="950" y1="172" x2="950" y2="200" class="connection"/>
                
                <!-- Activity Images Entity -->
                <rect x="1050" y="300" width="120" height="50" class="entity"/>
                <text x="1110" y="330" class="entity-text">รูปภาพผลงาน</text>
                
                <!-- Activity Images Attributes -->
                <ellipse cx="1000" cy="280" rx="30" ry="12" class="attribute"/>
                <text x="1000" y="280" class="attribute-text">รหัสรูปภาพ</text>
                <line x1="1030" y1="288" x2="1050" y2="310" class="connection"/>
                
                <!-- Contacts Entity -->
                <rect x="50" y="400" width="100" height="50" class="entity"/>
                <text x="100" y="430" class="entity-text">การติดต่อ</text>
                
                <!-- Contacts Attributes -->
                <ellipse cx="100" cy="360" rx="30" ry="12" class="attribute"/>
                <text x="100" y="360" class="attribute-text">รหัสการติดต่อ</text>
                <line x1="100" y1="372" x2="100" y2="400" class="connection"/>
                
                <ellipse cx="30" cy="480" rx="25" ry="12" class="attribute"/>
                <text x="30" y="480" class="attribute-text">ชื่อผู้ติดต่อ</text>
                <line x1="55" y1="468" x2="75" y2="450" class="connection"/>
                
                <!-- Banners Entity -->
                <rect x="300" y="400" width="100" height="50" class="entity"/>
                <text x="350" y="430" class="entity-text">แบนเนอร์</text>
                
                <!-- Site Settings Entity -->
                <rect x="500" y="500" width="120" height="50" class="entity"/>
                <text x="560" y="530" class="entity-text">การตั้งค่าเว็บไซต์</text>

                <!-- Visitors Entity -->
                <rect x="700" y="400" width="120" height="50" class="entity"/>
                <text x="760" y="430" class="entity-text">ผู้เยี่ยมชมเว็บไซต์</text>

                <!-- Visitors Attributes -->
                <ellipse cx="650" cy="380" rx="25" ry="12" class="attribute"/>
                <text x="650" y="380" class="attribute-text">รหัสผู้เยี่ยมชม</text>
                <line x1="675" y1="388" x2="700" y2="410" class="connection"/>

                <ellipse cx="760" cy="360" rx="20" ry="12" class="attribute"/>
                <text x="760" y="360" class="attribute-text">IP Address</text>
                <line x1="760" y1="372" x2="760" y2="400" class="connection"/>

                <ellipse cx="870" cy="380" rx="25" ry="12" class="attribute"/>
                <text x="870" y="380" class="attribute-text">เวลาเข้าชม</text>
                <line x1="845" y1="388" x2="820" y2="410" class="connection"/>

                <ellipse cx="760" cy="480" rx="25" ry="12" class="attribute"/>
                <text x="760" y="480" class="attribute-text">หน้าที่เข้าชม</text>
                <line x1="760" y1="468" x2="760" y2="450" class="connection"/>

                <ellipse cx="650" cy="460" rx="20" ry="12" class="attribute"/>
                <text x="650" y="460" class="attribute-text">User Agent</text>
                <line x1="675" y1="452" x2="700" y2="440" class="connection"/>

                <ellipse cx="870" cy="460" rx="20" ry="12" class="attribute"/>
                <text x="870" y="460" class="attribute-text">Referrer</text>
                <line x1="845" y1="452" x2="820" y2="440" class="connection"/>
                
                <!-- Relationships -->
                <!-- Services to Service Images -->
                <polygon points="420,250 440,230 460,250 440,270" class="relationship"/>
                <text x="440" y="250" class="relationship-text">มี</text>
                <line x1="400" y1="235" x2="420" y2="250" class="connection"/>
                <line x1="460" y1="250" x2="500" y2="315" class="connection"/>
                <text x="410" y="245" class="cardinality">1</text>
                <text x="480" y="285" class="cardinality">M</text>
                
                <!-- Activities to Activity Images -->
                <polygon points="1020,250 1040,230 1060,250 1040,270" class="relationship"/>
                <text x="1040" y="250" class="relationship-text">มี</text>
                <line x1="1000" y1="235" x2="1020" y2="250" class="connection"/>
                <line x1="1060" y1="250" x2="1100" y2="300" class="connection"/>
                <text x="1010" y="245" class="cardinality">1</text>
                <text x="1080" y="285" class="cardinality">M</text>
                
                <!-- Users manages Services -->
                <polygon points="220,150 240,130 260,150 240,170" class="relationship"/>
                <text x="240" y="150" class="relationship-text">จัดการ</text>
                <line x1="170" y1="100" x2="220" y2="150" class="connection"/>
                <line x1="260" y1="150" x2="300" y2="210" class="connection"/>
                <text x="190" y="125" class="cardinality">1</text>
                <text x="280" y="180" class="cardinality">M</text>
                
                <!-- Users manages Packages -->
                <polygon points="600,80 620,60 640,80 620,100" class="relationship"/>
                <text x="620" y="80" class="relationship-text">จัดการ</text>
                <line x1="170" y1="70" x2="600" y2="80" class="connection"/>
                <line x1="640" y1="80" x2="700" y2="75" class="connection"/>
                <text x="400" y="75" class="cardinality">1</text>
                <text x="670" y="78" class="cardinality">M</text>
                
                <!-- Users manages Activities -->
                <polygon points="800,150 820,130 840,150 820,170" class="relationship"/>
                <text x="820" y="150" class="relationship-text">จัดการ</text>
                <line x1="170" y1="90" x2="800" y2="150" class="connection"/>
                <line x1="840" y1="150" x2="900" y2="210" class="connection"/>
                <text x="500" y="120" class="cardinality">1</text>
                <text x="870" y="180" class="cardinality">M</text>
                
                <!-- Users views Contacts -->
                <polygon points="120,300 140,280 160,300 140,320" class="relationship"/>
                <text x="140" y="300" class="relationship-text">ดู</text>
                <line x1="110" y1="110" x2="120" y2="300" class="connection"/>
                <line x1="160" y1="300" x2="100" y2="400" class="connection"/>
                <text x="115" y="200" class="cardinality">1</text>
                <text x="130" y="350" class="cardinality">M</text>

                <!-- Visitors send Contacts -->
                <polygon points="600,450 620,430 640,450 620,470" class="relationship"/>
                <text x="620" y="450" class="relationship-text">ส่ง</text>
                <line x1="700" y1="425" x2="640" y2="450" class="connection"/>
                <line x1="600" y1="450" x2="150" y2="425" class="connection"/>
                <text x="670" y="438" class="cardinality">1</text>
                <text x="400" y="438" class="cardinality">M</text>

                <!-- Visitors view Services -->
                <polygon points="550,350 570,330 590,350 570,370" class="relationship"/>
                <text x="570" y="350" class="relationship-text">ดู</text>
                <line x1="700" y1="415" x2="590" y2="350" class="connection"/>
                <line x1="550" y1="350" x2="400" y2="225" class="connection"/>
                <text x="645" y="383" class="cardinality">M</text>
                <text x="475" y="288" class="cardinality">M</text>

                <!-- Visitors view Activities -->
                <polygon points="900,350 920,330 940,350 920,370" class="relationship"/>
                <text x="920" y="350" class="relationship-text">ดู</text>
                <line x1="820" y1="415" x2="900" y2="350" class="connection"/>
                <line x1="940" y1="350" x2="950" y2="250" class="connection"/>
                <text x="860" y="383" class="cardinality">M</text>
                <text x="945" y="300" class="cardinality">M</text>
            </svg>
        </div>
        
        <div class="summary">
            <h3>📋 สรุปโครงสร้าง E-R Diagram</h3>
            <div class="summary-grid">
                <div class="summary-item">
                    <h4>🏢 Entity (เอนทิตี) - 10 ตาราง</h4>
                    <ul>
                        <li>ผู้ดูแลระบบ (users)</li>
                        <li>บริการ (services)</li>
                        <li>รูปภาพบริการ (service_images)</li>
                        <li>แพ็คเกจ (packages)</li>
                        <li>ผลงาน (activities)</li>
                        <li>รูปภาพผลงาน (activity_images)</li>
                        <li>การติดต่อ (contacts)</li>
                        <li>แบนเนอร์ (banners)</li>
                        <li>การตั้งค่าเว็บไซต์ (site_settings)</li>
                        <li><strong>ผู้เยี่ยมชมเว็บไซต์ (visitors)</strong> 🆕</li>
                    </ul>
                </div>
                
                <div class="summary-item">
                    <h4>🔗 Relationship (ความสัมพันธ์)</h4>
                    <ul>
                        <li><strong>มี:</strong> บริการ → รูปภาพบริการ (1:M)</li>
                        <li><strong>มี:</strong> ผลงาน → รูปภาพผลงาน (1:M)</li>
                        <li><strong>จัดการ:</strong> ผู้ดูแลระบบ → บริการ (1:M)</li>
                        <li><strong>จัดการ:</strong> ผู้ดูแลระบบ → แพ็คเกจ (1:M)</li>
                        <li><strong>จัดการ:</strong> ผู้ดูแลระบบ → ผลงาน (1:M)</li>
                        <li><strong>ดู:</strong> ผู้ดูแลระบบ → การติดต่อ (1:M)</li>
                        <li><strong>ส่ง:</strong> ผู้เยี่ยมชม → การติดต่อ (1:M) 🆕</li>
                        <li><strong>ดู:</strong> ผู้เยี่ยมชม → บริการ (M:M) 🆕</li>
                        <li><strong>ดู:</strong> ผู้เยี่ยมชม → ผลงาน (M:M) 🆕</li>
                    </ul>
                </div>
                
                <div class="summary-item">
                    <h4>🎯 Cardinality (อัตราส่วน)</h4>
                    <ul>
                        <li><strong>1:M</strong> = หนึ่งต่อหลาย</li>
                        <li>บริการ 1 รายการ มีรูปภาพได้หลายรูป</li>
                        <li>ผลงาน 1 รายการ มีรูปภาพได้หลายรูป</li>
                        <li>ผู้ดูแล 1 คน จัดการได้หลายรายการ</li>
                    </ul>
                </div>
                
                <div class="summary-item">
                    <h4>🔑 Key Attributes (คีย์หลัก)</h4>
                    <ul>
                        <li>รหัสผู้ดูแล (Primary Key)</li>
                        <li>รหัสบริการ (Primary Key)</li>
                        <li>รหัสรูปภาพ (Primary Key)</li>
                        <li>รหัสแพ็คเกจ (Primary Key)</li>
                        <li>รหัสผลงาน (Primary Key)</li>
                        <li>รหัสการติดต่อ (Primary Key)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
