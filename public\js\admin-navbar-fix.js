// Phuyai Prajak Service Shop Admin - Navbar Overlap Fix
// ป้องกันการบังของ navbar กับเนื้อหา

document.addEventListener('DOMContentLoaded', function() {
    
    // ฟังก์ชันตรวจสอบและปรับปรุงตำแหน่งเนื้อหา
    function adjustContentPosition() {
        const navbar = document.querySelector('.top-navbar');
        const mainWrapper = document.querySelector('.main-wrapper');
        const contentSafeArea = document.querySelector('.content-safe-area');

        if (!navbar || !mainWrapper) return;

        // คำนวณความสูงของ navbar
        const navbarHeight = navbar.offsetHeight;
        const currentMarginTop = parseInt(window.getComputedStyle(mainWrapper).marginTop);

        // ตรวจสอบว่า margin-top เพียงพอหรือไม่
        if (currentMarginTop < navbarHeight + 15) {
            mainWrapper.style.marginTop = (navbarHeight + 15) + 'px';
            console.log('Adjusted main-wrapper margin-top to:', navbarHeight + 15 + 'px');
        }

        // เพิ่ม padding-top สำหรับ content-safe-area หากจำเป็น
        if (contentSafeArea) {
            const rect = contentSafeArea.getBoundingClientRect();
            if (rect.top < navbarHeight) {
                contentSafeArea.style.paddingTop = '2rem';
                contentSafeArea.style.marginTop = '1rem';
                console.log('Adjusted content-safe-area padding');
            }
        }

        // แก้ไขปัญหา layout ของ Bootstrap columns
        fixBootstrapLayout();
    }

    // ฟังก์ชันแก้ไขปัญหา Bootstrap layout
    function fixBootstrapLayout() {
        // แก้ไขปัญหา prevent-excessive-scroll
        const preventScrollElements = document.querySelectorAll('.prevent-excessive-scroll');
        preventScrollElements.forEach(element => {
            element.style.overflow = 'visible';
            element.style.height = 'auto';
            element.style.minHeight = 'auto';
        });

        // ตรวจสอบและแก้ไข Bootstrap columns
        const rows = document.querySelectorAll('.row');
        rows.forEach(row => {
            row.style.marginLeft = '0';
            row.style.marginRight = '0';
        });

        // แก้ไข col-lg-4 ที่อาจไปอยู่ด้านล่างผิด
        const rightColumns = document.querySelectorAll('.col-lg-4');
        rightColumns.forEach(col => {
            if (window.innerWidth >= 992) {
                col.style.order = '2';
            } else {
                col.style.order = '2';
                col.style.marginTop = '1rem';
            }
        });

        const leftColumns = document.querySelectorAll('.col-lg-8');
        leftColumns.forEach(col => {
            col.style.order = '1';
        });
    }
    
    // ฟังก์ชันตรวจสอบการทับซ้อน
    function checkOverlap() {
        const navbar = document.querySelector('.top-navbar');
        const firstElement = document.querySelector('.content-safe-area h1, .content-safe-area .h2, .content-safe-area .row:first-child');
        
        if (!navbar || !firstElement) return;
        
        const navbarRect = navbar.getBoundingClientRect();
        const elementRect = firstElement.getBoundingClientRect();
        
        // ตรวจสอบว่ามีการทับซ้อนหรือไม่
        if (elementRect.top < navbarRect.bottom + 10) {
            console.warn('Content overlap detected! Adjusting...');
            
            // เพิ่ม margin-top ให้กับ element แรก
            firstElement.style.marginTop = '2rem';
            
            // หรือเพิ่ม padding-top ให้กับ parent container
            const parent = firstElement.closest('.content-safe-area') || firstElement.closest('.main-wrapper');
            if (parent) {
                parent.style.paddingTop = '2rem';
            }
        }
    }
    
    // ฟังก์ชันสำหรับ responsive adjustment
    function handleResponsiveAdjustment() {
        const screenWidth = window.innerWidth;
        const navbar = document.querySelector('.top-navbar');
        const mainWrapper = document.querySelector('.main-wrapper');

        if (!navbar || !mainWrapper) return;

        if (screenWidth <= 768) {
            // Mobile adjustments
            navbar.style.height = '75px';
            mainWrapper.style.marginTop = '85px';
            mainWrapper.style.paddingTop = '0.5rem';
        } else {
            // Desktop adjustments
            navbar.style.height = '70px';
            mainWrapper.style.marginTop = '85px';
            mainWrapper.style.paddingTop = '1rem';
        }
    }

    // ปรับปรุงการทำงานของ navbar collapse
    function initNavbarCollapse() {
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');

        if (navbarToggler && navbarCollapse) {
            // เพิ่มเอฟเฟกต์เมื่อเปิด/ปิดเมนู
            navbarToggler.addEventListener('click', function() {
                setTimeout(() => {
                    if (navbarCollapse.classList.contains('show')) {
                        navbarCollapse.style.animation = 'slideDown 0.3s ease';
                    } else {
                        navbarCollapse.style.animation = 'slideUp 0.3s ease';
                    }
                }, 10);
            });

            // ปิดเมนูเมื่อคลิกข้างนอก
            document.addEventListener('click', function(event) {
                const isClickInsideNav = navbarCollapse.contains(event.target) ||
                                       navbarToggler.contains(event.target);

                if (!isClickInsideNav && navbarCollapse.classList.contains('show')) {
                    navbarToggler.click();
                }
            });

            // ปิดเมนูเมื่อคลิกลิงก์ (บนมือถือ)
            const navLinks = navbarCollapse.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 991.98 && navbarCollapse.classList.contains('show')) {
                        setTimeout(() => {
                            navbarToggler.click();
                        }, 150);
                    }
                });
            });
        }
    }

    // เพิ่ม scroll effect สำหรับ navbar
    function initNavbarScrollEffect() {
        const navbar = document.querySelector('.top-navbar');
        if (!navbar) return;

        let lastScrollTop = 0;

        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > 50) {
                navbar.classList.add('navbar-scrolled');
            } else {
                navbar.classList.remove('navbar-scrolled');
            }

            lastScrollTop = scrollTop;
        });
    }

    // ปรับปรุงการทำงานของ dropdown menu
    function initDropdownFix() {
        const dropdowns = document.querySelectorAll('.dropdown');

        dropdowns.forEach(dropdown => {
            const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
            const dropdownMenu = dropdown.querySelector('.dropdown-menu');

            if (dropdownToggle && dropdownMenu) {
                dropdownToggle.addEventListener('click', function() {
                    setTimeout(() => {
                        adjustDropdownPosition(dropdown, dropdownMenu);
                    }, 10);
                });

                // ปรับตำแหน่งเมื่อ resize
                window.addEventListener('resize', function() {
                    if (dropdownMenu.classList.contains('show')) {
                        adjustDropdownPosition(dropdown, dropdownMenu);
                    }
                });
            }
        });
    }

    // ฟังก์ชันปรับตำแหน่ง dropdown
    function adjustDropdownPosition(dropdown, dropdownMenu) {
        if (!dropdownMenu.classList.contains('show')) return;

        const rect = dropdown.getBoundingClientRect();
        const menuRect = dropdownMenu.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        // ตรวจสอบว่าเมนูล้นออกจากหน้าจอหรือไม่
        if (rect.bottom + menuRect.height > viewportHeight) {
            // ถ้าล้นด้านล่าง ให้แสดงด้านบน
            dropdownMenu.style.top = 'auto';
            dropdownMenu.style.bottom = '100%';
            dropdownMenu.style.marginBottom = '0.5rem';
            dropdownMenu.style.marginTop = '0';
        } else {
            // แสดงด้านล่างตามปกติ
            dropdownMenu.style.top = '100%';
            dropdownMenu.style.bottom = 'auto';
            dropdownMenu.style.marginTop = '0.5rem';
            dropdownMenu.style.marginBottom = '0';
        }

        // ปรับตำแหน่งซ้าย-ขวา
        if (rect.right - menuRect.width < 0) {
            dropdownMenu.style.left = '0';
            dropdownMenu.style.right = 'auto';
        }

        // สำหรับมือถือ
        if (viewportWidth <= 991.98) {
            dropdownMenu.style.position = 'fixed';
            dropdownMenu.style.left = '1rem';
            dropdownMenu.style.right = '1rem';
            dropdownMenu.style.width = 'auto';
            dropdownMenu.style.top = (rect.bottom + 10) + 'px';
            dropdownMenu.style.bottom = 'auto';
        }
    }
    
    // เรียกใช้ฟังก์ชันเมื่อโหลดหน้า
    adjustContentPosition();
    checkOverlap();
    handleResponsiveAdjustment();
    fixBootstrapLayout();
    initNavbarCollapse();
    initNavbarScrollEffect();
    initDropdownFix();
    
    // เรียกใช้ฟังก์ชันเมื่อ resize หน้าจอ
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            adjustContentPosition();
            checkOverlap();
            handleResponsiveAdjustment();
            fixBootstrapLayout();
        }, 250);
    });
    
    // เรียกใช้ฟังก์ชันเมื่อ scroll (สำหรับ sticky navbar)
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(function() {
            checkOverlap();
        }, 100);
    });
    
    // ฟังก์ชันสำหรับ smooth scroll ที่คำนึงถึง navbar
    function smoothScrollToElement(element, offset = 85) {
        if (!element) return;
        
        const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - offset;
        
        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    }
    
    // เพิ่มฟังก์ชัน smooth scroll ให้กับ anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                smoothScrollToElement(target);
            }
        });
    });
    
    // ฟังก์ชันสำหรับ debug (เฉพาะ development)
    function debugNavbarPosition() {
        const navbar = document.querySelector('.top-navbar');
        const mainWrapper = document.querySelector('.main-wrapper');
        
        if (navbar && mainWrapper) {
            console.log('Navbar height:', navbar.offsetHeight);
            console.log('Main wrapper margin-top:', window.getComputedStyle(mainWrapper).marginTop);
            console.log('Main wrapper padding-top:', window.getComputedStyle(mainWrapper).paddingTop);
        }
    }
    
    // เรียกใช้ debug function (สามารถลบออกได้ใน production)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        debugNavbarPosition();
    }
    
    console.log('Admin navbar overlap fix initialized successfully! 🚀');
});

// Export functions สำหรับใช้งานภายนอก
window.AdminNavbarFix = {
    adjustContentPosition: function() {
        // สามารถเรียกใช้จากภายนอกได้
        const event = new Event('DOMContentLoaded');
        document.dispatchEvent(event);
    },
    
    checkOverlap: function() {
        // ตรวจสอบการทับซ้อนแบบ manual
        const navbar = document.querySelector('.top-navbar');
        const firstElement = document.querySelector('.content-safe-area h1, .content-safe-area .h2');
        
        if (navbar && firstElement) {
            const navbarRect = navbar.getBoundingClientRect();
            const elementRect = firstElement.getBoundingClientRect();
            
            return elementRect.top < navbarRect.bottom + 10;
        }
        
        return false;
    }
};
