/* <PERSON><PERSON><PERSON>ak Service Shop Admin - Professional Clean Design */

:root {
    /* สีหลัก - Professional Blue Theme */
    --primary-color: #3B82F6;
    --primary-hover: #2563EB;
    --primary-light: #DBEAFE;
    --secondary-color: #6366F1;
    --secondary-hover: #4F46E5;

    /* สีพื้นหลัง */
    --bg-primary: #F8FAFC;
    --bg-secondary: #F1F5F9;
    --bg-surface: #FFFFFF;
    --bg-hover: #F8FAFC;

    /* สีขอบ */
    --border-light: #E2E8F0;
    --border-medium: #CBD5E1;
    --border-dark: #94A3B8;

    /* สีข้อความ */
    --text-primary: #1E293B;
    --text-secondary: #64748B;
    --text-muted: #94A3B8;
    --text-white: #FFFFFF;

    /* สีสถานะ */
    --success-color: #10B981;
    --success-light: #D1FAE5;
    --warning-color: #F59E0B;
    --warning-light: #FEF3C7;
    --danger-color: #EF4444;
    --danger-light: #FEE2E2;
    --info-color: #06B6D4;
    --info-light: #CFFAFE;

    /* เงา */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    font-family: 'Inter', 'Kanit', sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    transition: all 0.3s ease;
    height: auto;
    overflow-x: hidden;
}

/* Container และ Layout ปรับปรุง */
.container, .container-fluid {
    height: auto;
    overflow: visible;
}

.main-content {
    height: auto;
    overflow: visible;
}

/* Main Wrapper - ป้องกันการบังโดย navbar */
.main-wrapper {
    margin-top: 85px !important; /* ป้องกันการบังโดย fixed navbar */
    padding-top: 1rem !important;
    min-height: calc(100vh - 85px);
}

/* Content Safe Area - utility class สำหรับป้องกันการบัง */
.content-safe-area {
    padding-top: 1rem;
    margin-top: 0.5rem;
}

/* ป้องกันการบังสำหรับ alerts */
.alert {
    margin-top: 0.5rem;
}

/* แก้ไขปัญหา layout สำหรับหน้าแก้ไข */
.prevent-excessive-scroll {
    overflow: visible !important;
    height: auto !important;
    min-height: auto !important;
}

/* ป้องกันการเสีย layout ของ Bootstrap columns */
.row {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.col-lg-8, .col-lg-4, .col-md-8, .col-md-4 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* ให้แน่ใจว่า sidebar columns อยู่ในตำแหน่งที่ถูกต้อง */
@media (min-width: 992px) {
    .col-lg-4 {
        order: 2; /* ให้อยู่ด้านขวา */
    }

    .col-lg-8 {
        order: 1; /* ให้อยู่ด้านซ้าย */
    }
}

/* สำหรับมือถือให้ sidebar อยู่ด้านล่าง */
@media (max-width: 991.98px) {
    .col-lg-4 {
        order: 2;
        margin-top: 1rem;
    }

    .col-lg-8 {
        order: 1;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* ป้องกันการเลื่อนที่ไม่จำเป็น */
.row {
    margin-left: 0;
    margin-right: 0;
}

.col, .col-* {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* ปรับปรุงการแสดงผลของ Card */
.card {
    height: auto;
    overflow: visible;
    margin-bottom: 1.5rem;
}

.card-body {
    height: auto;
    overflow: visible;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Utility Classes */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.spin {
    animation: spin 1s linear infinite;
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.3s ease;
    cursor: pointer;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Button Loading State */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Progress Bar */
.progress-modern {
    height: 8px;
    border-radius: 4px;
    background: var(--dark-border);
    overflow: hidden;
}

.progress-modern .progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* Status Indicators */
.status-online {
    color: var(--success-color);
}

.status-offline {
    color: var(--danger-color);
}

.status-pending {
    color: var(--warning-color);
}

/* Interactive Elements */
.interactive-card {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.interactive-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none !important;
    }
    
    .mobile-full {
        width: 100% !important;
    }
    
    .mobile-center {
        text-align: center !important;
    }
}

/* Dark/Light Theme Toggle */
.theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Image Fit - Admin Version - แสดงรูปภาพทั้งหมดโดยไม่ตัด */
.img-fit-contain {
    object-fit: contain !important;
    object-position: center;
    background-color: var(--bg-secondary);
}

/* Admin Image Containers */
.img-container-fixed {
    position: relative;
    overflow: hidden;
    background-color: var(--bg-secondary);
    border-radius: 8px;
}

.img-container-fixed img {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
}

/* Admin Image Sizes */
.img-size-thumbnail {
    height: 100px;
}

.img-size-small {
    height: 150px;
}

.img-size-medium {
    height: 200px;
}

.img-size-large {
    height: 250px;
}

.img-size-xlarge {
    height: 400px;
}

/* Admin Card Image Styles */
.card-image-container {
    position: relative;
    overflow: hidden;
    background-color: var(--bg-secondary);
}

.card-image-container img {
    width: 100%;
    transition: transform 0.3s ease;
}

.card-image-container:hover img {
    transform: scale(1.02);
}

/* Admin Gallery Image Styles */
.gallery-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    background-color: var(--bg-secondary);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.gallery-image-container:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.gallery-image-container img {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
}

.gallery-image-container:hover img {
    transform: scale(1.05);
}

/* Custom Form Controls */
.form-control-modern {
    background: rgba(51, 65, 85, 0.5);
    border: 1px solid var(--dark-border);
    border-radius: 12px;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-control-modern:focus {
    background: rgba(51, 65, 85, 0.8);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
    color: var(--text-primary);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 23, 42, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid var(--dark-border);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced Cards */
.card {
    background: var(--bg-surface);
    border: 1px solid var(--border-light);
    border-radius: 16px;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: var(--border-medium);
}

.card-header {
    background: linear-gradient(135deg, var(--bg-surface) 0%, var(--bg-secondary) 100%);
    border-bottom: 1px solid var(--border-light);
    padding: 1.25rem;
    font-weight: 600;
}

/* Enhanced Buttons */
.btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--text-white);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, var(--secondary-hover) 100%);
    color: var(--text-white);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: var(--text-white);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #D97706 100%);
    color: var(--text-white);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #0891B2 100%);
    color: var(--text-white);
}

/* Enhanced Alerts */
.alert {
    border-radius: 12px;
    border: none;
    padding: 16px 20px;
    margin-bottom: 20px;
    position: relative;
}

.alert-success {
    background: var(--success-light);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: var(--danger-light);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: var(--warning-light);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: var(--info-light);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* Form Controls */
.form-control {
    border-radius: 10px;
    border: 2px solid var(--border-light);
    padding: 12px 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: var(--bg-surface);
    color: var(--text-primary);
    position: relative;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.form-control:hover:not(:focus) {
    border-color: var(--border-medium);
    transform: translateY(-0.5px);
}

/* Smooth Button Animations */
.btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
    transition: all 0.1s ease;
}

/* Ripple Effect */
.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 8px;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 16px;
    margin-bottom: 8px;
}

.skeleton-title {
    height: 24px;
    width: 60%;
    margin-bottom: 12px;
}

.skeleton-image {
    height: 200px;
    width: 100%;
}

/* Smooth Card Animations */
.card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--border-light);
    background: var(--bg-surface);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

/* Smooth Table Animations */
.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: var(--bg-hover);
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Smooth Navigation */
.nav-link {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 100%;
}

.nav-link:hover {
    transform: translateY(-2px);
    color: var(--primary-color);
}

/* Page Transitions */
.page-transition {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Smooth Modal Animations */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

/* Smooth Alert Animations */
.alert {
    transform: translateX(-100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.alert.show {
    transform: translateX(0);
}

/* Image Upload Preview Animations */
.image-preview {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.image-preview.loaded {
    opacity: 1;
    transform: scale(1);
}

/* Smooth Dropdown Animations */
.dropdown-menu {
    transform: translateY(-10px);
    opacity: 0;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-menu.show {
    transform: translateY(0);
    opacity: 1;
}

/* View Toggle Animations */
#tableView, #cardView {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Card View Enhancements */
.activity-card {
    transition: all 0.3s ease;
}

.activity-card:hover {
    transform: translateY(-2px);
}

.activity-card .card {
    border: 1px solid var(--border-light);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.activity-card .card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* Card Image Container */
.card-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.card-image-container img {
    transition: transform 0.3s ease;
}

.card-image-container:hover img {
    transform: scale(1.05);
}

.card-image-container .hover-overlay {
    transition: opacity 0.3s ease;
}

.card-image-container:hover .hover-overlay {
    opacity: 1 !important;
}

/* Badge Animations */
.badge {
    transition: all 0.2s ease;
}

.badge:hover {
    transform: scale(1.05);
}

/* Button Loading State */
.btn:disabled {
    opacity: 0.7;
    transform: none !important;
}

.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

/* Enhanced Pagination Styles */
.pagination {
    margin: 0;
    gap: 4px;
}

.pagination .page-item {
    margin: 0;
}

.pagination .page-link {
    border-radius: 8px;
    border: 1px solid var(--border-light);
    color: var(--text-primary);
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--bg-surface);
    min-width: 40px;
    text-align: center;
}

.pagination .page-link:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.pagination .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);
}

.pagination .page-item.disabled .page-link {
    background: var(--bg-secondary);
    border-color: var(--border-light);
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination .page-item.disabled .page-link:hover {
    background: var(--bg-secondary);
    border-color: var(--border-light);
    color: var(--text-muted);
    transform: none;
    box-shadow: none;
}

/* Pagination Info */
.pagination-info {
    background: var(--bg-surface);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    color: var(--text-secondary);
}

/* Page Jump Select */
.pagination-jump .form-select {
    border-radius: 6px;
    border: 1px solid var(--border-light);
    padding: 4px 8px;
    font-size: 13px;
    min-width: 60px;
    transition: all 0.3s ease;
}

.pagination-jump .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Responsive Pagination */
@media (max-width: 768px) {
    .pagination .page-link {
        padding: 6px 8px;
        font-size: 12px;
        min-width: 32px;
    }

    .pagination-info {
        font-size: 12px;
        padding: 6px 8px;
    }

    .pagination-jump {
        display: none;
    }
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
    background-color: var(--bg-surface);
    color: var(--text-primary);
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, var(--bg-surface) 0%, var(--bg-secondary) 100%) !important;
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
}

/* แก้ไขปัญหาเมนูแฮมเบอร์เกอร์โปร่งใส */
.navbar-collapse {
    background: var(--bg-surface) !important;
    border-radius: 12px;
    margin-top: 0.5rem;
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
}

/* ปรับปรุงสำหรับมือถือ */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background: rgba(255, 255, 255, 0.98) !important;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-light);
        border-radius: 12px;
        margin-top: 0.75rem;
        padding: 1.25rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1000;
    }

    .navbar-nav {
        background: transparent;
        padding: 0;
    }

    .nav-item {
        margin-bottom: 0.5rem;
    }

    .nav-link {
        background: rgba(248, 250, 252, 0.8) !important;
        border: 1px solid var(--border-light);
        border-radius: 8px;
        padding: 12px 16px !important;
        margin-bottom: 0.5rem;
        color: var(--text-primary) !important;
        font-weight: 600;
    }

    .nav-link:hover {
        background: var(--primary-light) !important;
        color: var(--primary-color) !important;
        border-color: var(--primary-color);
        transform: translateX(4px);
    }

    .nav-link.active {
        background: var(--primary-color) !important;
        color: white !important;
        border-color: var(--primary-color);
    }
}

.nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    background: linear-gradient(135deg, var(--primary-light) 0%, rgba(59, 130, 246, 0.1) 100%) !important;
    color: var(--primary-color) !important;
    transform: translateY(-1px);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    color: var(--text-white) !important;
    box-shadow: var(--shadow-md);
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) 1;
}

.shadow-primary {
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.25);
}

.shadow-success {
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.25);
}

.shadow-warning {
    box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.25);
}

.shadow-danger {
    box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.25);
}

/* Ripple Effect */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Enhanced Stats Cards */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--text-white);
    border: none;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

/* Loading States */
.btn-loading {
    pointer-events: none;
    opacity: 0.7;
    position: relative;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Hover Effects */
.pulse-hover:hover {
    animation: pulse 0.6s ease-in-out;
}

.card-hover {
    transition: all 0.3s ease;
    cursor: pointer;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* List Groups */
.list-group-item {
    border: 1px solid var(--border-light);
    background-color: var(--bg-surface);
    color: var(--text-primary);
    border-radius: 10px !important;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: var(--bg-hover);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

/* Badges */
.badge {
    border-radius: 8px;
    padding: 6px 12px;
    font-weight: 500;
    font-size: 0.75rem;
}

/* ป้องกันการเลื่อนเกินจำเป็น */
.prevent-excessive-scroll {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    overflow: visible !important;
}

/* ปรับปรุงการแสดงผลของหน้าแก้ไข */
.edit-page-container {
    height: auto;
    min-height: auto;
    overflow: visible;
    padding-bottom: 2rem;
}

/* ปรับปรุงการแสดงผลของ Row และ Column */
.row {
    height: auto;
    min-height: auto;
}

.col, [class*="col-"] {
    height: auto;
    min-height: auto;
}

/* ปรับปรุงการแสดงผลของ Modal */
.modal-dialog {
    height: auto;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content {
    height: auto;
    max-height: none;
}

/* ปรับปรุงการแสดงผลของ Form */
.form-group, .mb-3, .mb-4 {
    height: auto;
    overflow: visible;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none !important;
    }

    .mobile-full {
        width: 100% !important;
    }

    .mobile-center {
        text-align: center !important;
    }

    .card {
        margin-bottom: 1rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    /* ปรับปรุงการแสดงผลบนมือถือ */
    .main-wrapper {
        margin-top: 85px !important; /* ป้องกันการบังบนมือถือ */
        padding: 1rem !important;
        padding-top: 0.5rem !important;
        padding-bottom: 2rem !important;
    }

    /* เพิ่มการป้องกันการบังสำหรับมือถือ */
    .content-safe-area {
        padding-top: 1.5rem;
        margin-top: 1rem;
    }

    /* ปรับปรุง navbar สำหรับมือถือ */
    .top-navbar {
        height: 75px !important;
    }

    /* ปรับปรุงปุ่มแฮมเบอร์เกอร์ */
    .navbar-toggler {
        border: 2px solid var(--border-light) !important;
        border-radius: 8px !important;
        padding: 8px 12px !important;
        background: var(--bg-surface) !important;
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
    }

    .navbar-toggler:hover {
        border-color: var(--primary-color) !important;
        background: var(--primary-light) !important;
        transform: scale(1.05);
    }

    .navbar-toggler:focus {
        box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25) !important;
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2830, 41, 59, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
        width: 20px;
        height: 20px;
    }
}

/* ปรับปรุง Dropdown Menu */
.dropdown-menu {
    background: var(--bg-surface) !important;
    border: 1px solid var(--border-light) !important;
    border-radius: 12px !important;
    box-shadow: var(--shadow-lg) !important;
    padding: 0.75rem !important;
    margin-top: 0.5rem !important;
    min-width: 250px !important;
    max-height: none !important;
    overflow: visible !important;
    z-index: 1050 !important;
    position: absolute !important;
}

/* แก้ไขปัญหาการแสดงผล dropdown บนมือถือ */
@media (max-width: 991.98px) {
    .dropdown-menu {
        position: fixed !important;
        top: auto !important;
        left: 1rem !important;
        right: 1rem !important;
        width: auto !important;
        min-width: auto !important;
        margin-top: 0.25rem !important;
        transform: none !important;
        z-index: 1060 !important;
    }

    .dropdown-menu.dropdown-menu-end {
        left: 1rem !important;
        right: 1rem !important;
    }
}

.dropdown-item {
    color: var(--text-primary) !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    margin-bottom: 0.25rem !important;
    transition: all 0.3s ease;
    font-weight: 500;
    white-space: nowrap;
    display: flex !important;
    align-items: center;
}

.dropdown-item:hover {
    background: var(--primary-light) !important;
    color: var(--primary-color) !important;
    transform: translateX(4px);
}

.dropdown-item:focus {
    background: var(--primary-light) !important;
    color: var(--primary-color) !important;
    outline: none;
}

.dropdown-item i {
    width: 20px;
    text-align: center;
}

.dropdown-divider {
    border-color: var(--border-light) !important;
    margin: 0.75rem 0 !important;
}

/* แก้ไขปัญหา dropdown ถูกตัดโดย navbar */
.navbar .dropdown {
    position: static;
}

@media (min-width: 992px) {
    .navbar .dropdown {
        position: relative;
    }
}

/* แก้ไขปัญหา dropdown ถูกซ่อนโดย overflow */
.navbar-nav,
.navbar-collapse,
.container-fluid {
    overflow: visible !important;
}

/* ปรับปรุงการแสดงผล dropdown form elements */
.dropdown-item form {
    margin: 0 !important;
    padding: 0 !important;
}

.dropdown-item button[type="submit"] {
    background: none !important;
    border: none !important;
    color: inherit !important;
    font: inherit !important;
    text-align: left !important;
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    cursor: pointer;
}

.dropdown-item button[type="submit"]:hover {
    background: none !important;
    color: inherit !important;
}

/* เพิ่มการรองรับ dropdown ที่มีเนื้อหาเยอะ */
.dropdown-menu {
    max-height: 400px !important;
    overflow-y: auto !important;
}

/* ปรับปรุงการแสดงผลบน viewport เล็ก */
@media (max-width: 575.98px) {
    .dropdown-menu {
        left: 0.5rem !important;
        right: 0.5rem !important;
        margin-top: 0.25rem !important;
        max-height: 300px !important;
    }
}

/* แก้ไขปัญหา z-index conflicts */
.navbar {
    z-index: 1030 !important;
}

.dropdown-menu {
    z-index: 1060 !important;
}

.navbar-collapse.show {
    z-index: 1040 !important;
}

/* ปรับปรุง Navbar Brand */
.navbar-brand {
    color: var(--text-primary) !important;
    font-weight: 700;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.navbar-brand:hover {
    color: var(--primary-color) !important;
    transform: scale(1.02);
}

.navbar-brand img {
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.navbar-brand:hover img {
    filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3));
}

/* ปรับปรุงการแสดงผล Badge */
.badge {
    font-size: 0.7rem;
    padding: 0.35em 0.6em;
    border-radius: 6px;
    font-weight: 600;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* ปรับปรุงการแสดงผลของ Fixed Navbar */
.top-navbar {
    position: fixed !important;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    height: 70px;
    transition: all 0.3s ease;
}

/* เพิ่มเอฟเฟกต์เมื่อ scroll */
.navbar-scrolled {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Animations สำหรับ Navbar Collapse */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* ปรับปรุงการแสดงผลของ Navbar Collapse */
.navbar-collapse.collapsing {
    transition: height 0.35s ease;
}

.navbar-collapse.show {
    animation: slideDown 0.3s ease;
}

/* เพิ่มเอฟเฟกต์ hover สำหรับ navbar-toggler */
.navbar-toggler {
    position: relative;
    overflow: hidden;
}

.navbar-toggler::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(59, 130, 246, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.navbar-toggler:hover::before {
    width: 100%;
    height: 100%;
}

/* ปรับปรุงการแสดงผลของ Active Nav Link */
.nav-link.active {
    background: var(--primary-color) !important;
    color: white !important;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.nav-link.active::before {
    width: 100% !important;
    background: white !important;
}
