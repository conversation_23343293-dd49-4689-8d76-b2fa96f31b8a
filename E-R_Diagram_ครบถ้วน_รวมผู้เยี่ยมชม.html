<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-R Diagram ครบถ้วน - รวมผู้เยี่ยมชมเว็บไซต์</title>
    <style>
        body {
            font-family: 'Sarabun', 'TH Sarabun New', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 18px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .highlight-box h3 {
            margin: 0 0 15px 0;
            font-size: 22px;
        }
        
        .visitors-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 30px 0;
        }
        
        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .table-header h4 {
            margin: 0;
            font-size: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th {
            background: #f8f9fa;
            padding: 15px 10px;
            text-align: left;
            font-weight: bold;
            border-bottom: 2px solid #dee2e6;
        }
        
        td {
            padding: 12px 10px;
            border-bottom: 1px solid #eee;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        tr:hover {
            background-color: #e3f2fd;
        }
        
        .key-pk {
            background: #ff6b6b;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .key-fk {
            background: #4ecdc4;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .usage-box {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .relationship-box {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .comparison-item {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .comparison-item h4 {
            color: #667eea;
            margin: 0 0 15px 0;
            font-size: 18px;
        }
        
        .status-current {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .status-proposed {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .benefits-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .benefits-list h5 {
            color: #28a745;
            margin: 0 0 15px 0;
        }
        
        .benefits-list ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .benefits-list li {
            margin: 8px 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>E-R Diagram ครบถ้วน</h1>
            <h2>รวมผู้เยี่ยมชมเว็บไซต์ - ระบบฐานข้อมูลเว็บไซต์ผู้ใหญ่ประจักร์ เซอร์วิส ช็อป</h2>
            <p>วันที่จัดทำ: 21 กรกฎาคม 2568</p>
        </div>
        
        <div class="content">
            <div class="highlight-box">
                <h3>🆕 เพิ่มเติม: ตาราง Visitors (ผู้เยี่ยมชมเว็บไซต์)</h3>
                <p>เพื่อให้ E-R Diagram สมบูรณ์และสะท้อนการใช้งานจริงของเว็บไซต์</p>
            </div>
            
            <div class="visitors-table">
                <div class="table-header">
                    <h4>🌐 ตาราง visitors - ผู้เยี่ยมชมเว็บไซต์</h4>
                </div>
                <div class="usage-box">
                    💼 <strong>การใช้งาน:</strong> ติดตามสถิติผู้เข้าชม, วิเคราะห์พฤติกรรมผู้ใช้, ระบบ Analytics
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>ลำดับ</th>
                            <th>ชื่อฟิลด์</th>
                            <th>ประเภทข้อมูล</th>
                            <th>คีย์</th>
                            <th>คำอธิบาย</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>id</td>
                            <td>BIGINT</td>
                            <td><span class="key-pk">PK</span></td>
                            <td>รหัสการเข้าชม (Auto Increment)</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>ip_address</td>
                            <td>VARCHAR(45)</td>
                            <td>-</td>
                            <td>IP Address ของผู้เข้าชม (รองรับ IPv6)</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>user_agent</td>
                            <td>TEXT</td>
                            <td>-</td>
                            <td>ข้อมูล Browser และ OS</td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>page_visited</td>
                            <td>VARCHAR(255)</td>
                            <td>-</td>
                            <td>หน้าที่เข้าชม (URL Path)</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>referrer</td>
                            <td>VARCHAR(255)</td>
                            <td>-</td>
                            <td>เว็บไซต์ที่มาจาก (Referrer URL)</td>
                        </tr>
                        <tr>
                            <td>6</td>
                            <td>session_id</td>
                            <td>VARCHAR(255)</td>
                            <td>-</td>
                            <td>รหัส Session (เชื่อมโยงการเข้าชมในครั้งเดียวกัน)</td>
                        </tr>
                        <tr>
                            <td>7</td>
                            <td>visit_duration</td>
                            <td>INT</td>
                            <td>-</td>
                            <td>ระยะเวลาที่อยู่ในหน้า (วินาที)</td>
                        </tr>
                        <tr>
                            <td>8</td>
                            <td>device_type</td>
                            <td>ENUM</td>
                            <td>-</td>
                            <td>ประเภทอุปกรณ์ (desktop, mobile, tablet)</td>
                        </tr>
                        <tr>
                            <td>9</td>
                            <td>country</td>
                            <td>VARCHAR(100)</td>
                            <td>-</td>
                            <td>ประเทศ (จาก IP Geolocation)</td>
                        </tr>
                        <tr>
                            <td>10</td>
                            <td>city</td>
                            <td>VARCHAR(100)</td>
                            <td>-</td>
                            <td>เมือง (จาก IP Geolocation)</td>
                        </tr>
                        <tr>
                            <td>11</td>
                            <td>visited_at</td>
                            <td>TIMESTAMP</td>
                            <td>-</td>
                            <td>วันเวลาที่เข้าชม</td>
                        </tr>
                        <tr>
                            <td>12</td>
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>-</td>
                            <td>วันที่บันทึกข้อมูล</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="relationship-box">
                <strong>🔗 ความสัมพันธ์ใหม่ที่เพิ่มเข้ามา:</strong><br>
                1. <strong>visitors → contacts</strong> (1:M) - ผู้เยี่ยมชมสามารถส่งข้อความติดต่อได้หลายครั้ง<br>
                2. <strong>visitors ↔ services</strong> (M:M) - ผู้เยี่ยมชมสามารถดูบริการได้หลายรายการ<br>
                3. <strong>visitors ↔ activities</strong> (M:M) - ผู้เยี่ยมชมสามารถดูผลงานได้หลายรายการ
            </div>
            
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4>📊 ระบบปัจจุบัน</h4>
                    <div class="status-current">
                        <strong>⚠️ ไม่มีการติดตามผู้เยี่ยมชม</strong>
                    </div>
                    <ul>
                        <li>มีเพียงระบบ Admin (users)</li>
                        <li>ไม่ทราบจำนวนผู้เข้าชม</li>
                        <li>ไม่สามารถวิเคราะห์พฤติกรรมผู้ใช้</li>
                        <li>ไม่ทราบหน้าไหนได้รับความนิยม</li>
                        <li>ไม่มีข้อมูลสำหรับปรับปรุงเว็บไซต์</li>
                    </ul>
                </div>
                
                <div class="comparison-item">
                    <h4>🚀 ระบบที่เสนอ (รวม Visitors)</h4>
                    <div class="status-proposed">
                        <strong>✅ มีการติดตามผู้เยี่ยมชมครบถ้วน</strong>
                    </div>
                    <ul>
                        <li>ติดตามสถิติผู้เข้าชมแบบ Real-time</li>
                        <li>วิเคราะห์พฤติกรรมผู้ใช้</li>
                        <li>ทราบหน้าที่ได้รับความนิยม</li>
                        <li>ข้อมูลสำหรับปรับปรุงเว็บไซต์</li>
                        <li>รองรับการทำ SEO และ Marketing</li>
                    </ul>
                </div>
            </div>
            
            <div class="benefits-list">
                <h5>🎯 ประโยชน์ของการเพิ่มตาราง Visitors:</h5>
                <ul>
                    <li><strong>📈 Analytics:</strong> ติดตามสถิติการเข้าชมแบบละเอียด</li>
                    <li><strong>🎯 Marketing:</strong> ทราบกลุ่มเป้าหมายและพฤติกรรมลูกค้า</li>
                    <li><strong>🔍 SEO:</strong> วิเคราะห์หน้าไหนได้รับความนิยม</li>
                    <li><strong>📱 UX/UI:</strong> ปรับปรุงการออกแบบตามพฤติกรรมผู้ใช้</li>
                    <li><strong>🌍 Geographic:</strong> ทราบพื้นที่ที่ลูกค้ามาจาก</li>
                    <li><strong>📊 Business Intelligence:</strong> ข้อมูลสำหรับตัดสินใจทางธุรกิจ</li>
                    <li><strong>🔒 Security:</strong> ติดตามการเข้าถึงที่ผิดปกติ</li>
                    <li><strong>⚡ Performance:</strong> วิเคราะห์ความเร็วและประสิทธิภาพ</li>
                </ul>
            </div>
            
            <div class="visitors-table">
                <div class="table-header">
                    <h4>📋 สรุปโครงสร้าง E-R Diagram ฉบับสมบูรณ์</h4>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>ประเภท</th>
                            <th>จำนวน</th>
                            <th>รายการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Entity (ตาราง)</strong></td>
                            <td><strong>10 ตาราง</strong></td>
                            <td>users, services, service_images, packages, activities, activity_images, contacts, banners, site_settings, <span style="color: #ff6b6b; font-weight: bold;">visitors</span></td>
                        </tr>
                        <tr>
                            <td><strong>Relationship หลัก</strong></td>
                            <td><strong>5 ความสัมพันธ์</strong></td>
                            <td>services→service_images, activities→activity_images, users→(จัดการ), <span style="color: #ff6b6b; font-weight: bold;">visitors→contacts, visitors↔services/activities</span></td>
                        </tr>
                        <tr>
                            <td><strong>ตารางใช้งานจริง</strong></td>
                            <td><strong>9 ตาราง</strong></td>
                            <td>ทุกตารางยกเว้น migrations</td>
                        </tr>
                        <tr>
                            <td><strong>ตารางระบบ</strong></td>
                            <td><strong>1 ตาราง</strong></td>
                            <td>migrations (Laravel Framework)</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="highlight-box" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <h3>✅ สรุป</h3>
                <p>E-R Diagram ฉบับนี้ครบถ้วนสมบูรณ์ สะท้อนการใช้งานจริงของเว็บไซต์<br>
                รวมทั้งผู้ดูแลระบบ (Admin) และผู้เยี่ยมชมเว็บไซต์ (Visitors) ครบถ้วน</p>
            </div>
        </div>
    </div>
</body>
</html>
